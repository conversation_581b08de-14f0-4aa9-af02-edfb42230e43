Log started at 7/5/2025 2:41:21 AM
2025-07-05 02:41:21.608 [Information] LoggingService: Logging service initialized
2025-07-05 02:41:21.619 [Information] App: Starting integrated application initialization
2025-07-05 02:41:21.621 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 02:41:21.622 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 02:41:21.629 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 02:41:21.629 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 02:41:21.631 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 02:41:21.633 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 02:41:21.643 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 02:41:21.755 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 02:41:21.759 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 02:41:21.763 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 02:41:21.764 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 02:41:21.776 [Debug] VCRedistBundler: Successfully loaded and verified: msvcr120.dll
2025-07-05 02:41:21.777 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 02:41:21.782 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp120.dll
2025-07-05 02:41:21.784 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 02:41:21.786 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 02:41:21.795 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-05 02:41:21.795 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 02:41:21.801 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-05 02:41:21.803 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 02:41:21.805 [Debug] VCRedistBundler: Successfully loaded and verified: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 02:41:21.805 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 02:41:21.806 [Debug] VCRedistBundler: Successfully loaded and verified: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 02:41:21.806 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 02:41:21.807 [Debug] VCRedistBundler: Successfully loaded and verified: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 02:41:21.810 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-05 02:41:21.810 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-05 02:41:21.819 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-05 02:41:21.820 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-05 02:41:21.822 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 02:41:21.824 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 02:41:21.831 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 02:41:21.838 [Information] LibraryExtractor: Copying system libraries
2025-07-05 02:41:21.849 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 02:41:21.856 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 02:41:39.171 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 02:42:04.964 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 02:42:35.798 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 02:42:35.798 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 02:42:35.798 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 02:42:35.799 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 02:42:35.799 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 02:42:35.799 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 02:42:35.806 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 02:42:35.808 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 02:42:35.809 [Information] DependencyManager: Initializing dependency manager
2025-07-05 02:42:35.809 [Information] DependencyManager: Setting up library search paths
2025-07-05 02:42:35.811 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:35.811 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 02:42:35.811 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 02:42:35.812 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 02:42:35.815 [Information] DependencyManager: Verifying required directories
2025-07-05 02:42:35.816 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:35.816 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 02:42:35.816 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 02:42:35.816 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 02:42:35.819 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 02:42:35.831 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 02:42:35.832 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 02:42:35.837 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 02:42:35.839 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 02:42:35.840 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 02:42:35.841 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 02:42:35.841 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 02:42:35.842 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 02:42:35.845 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 02:42:35.849 [Debug] DependencyManager: Architecture mismatch: Library WUDFPuma.dll is x64, process is x86
2025-07-05 02:42:35.849 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-05 02:42:35.850 [Warning] DependencyManager: Failed to load Critical library WUDFPuma.dll: Error 193
2025-07-05 02:42:35.934 [Information] DependencyManager: ✓ Loaded Critical library: apci.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll (x86)
2025-07-05 02:42:35.994 [Information] DependencyManager: ✓ Loaded Critical library: apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll (x86)
2025-07-05 02:42:36.249 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlus.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll (x86)
2025-07-05 02:42:36.342 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll (x86)
2025-07-05 02:42:36.408 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll (x86)
2025-07-05 02:42:36.409 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 02:42:36.409 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 02:42:36.410 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 02:42:36.411 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 02:42:36.411 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 02:42:36.411 [Information] DependencyManager: Setting up environment variables
2025-07-05 02:42:36.412 [Information] DependencyManager: Environment variables configured
2025-07-05 02:42:36.415 [Information] DependencyManager: Verifying library loading status
2025-07-05 02:42:36.644 [Information] DependencyManager: Library loading verification: 9/11 (81.8%) critical libraries loaded
2025-07-05 02:42:36.644 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 02:42:36.648 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 02:42:36.649 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 02:42:36.652 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 02:42:36.657 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 02:42:36.658 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 02:42:36.658 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 02:42:36.661 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 02:42:36.662 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 02:42:36.662 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:36.662 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 02:42:36.663 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 02:42:36.663 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 02:42:36.664 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 02:42:36.664 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:36.664 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 02:42:36.664 [Information] App: Integrated startup completed successfully
2025-07-05 02:42:36.667 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 02:42:36.859 [Information] App: Initializing application services
2025-07-05 02:42:36.860 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 02:42:36.861 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 02:42:36.909 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 02:42:36.910 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 02:42:36.910 [Information] App: Configuration service initialized successfully
2025-07-05 02:42:36.911 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 02:42:36.912 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 02:42:36.915 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 02:42:36.915 [Information] App: Final useDummyImplementations value: False
2025-07-05 02:42:36.916 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 02:42:36.917 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-05 02:42:36.928 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 02:42:36.928 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 02:42:36.929 [Information] App: usePatchedImplementation flag is: True
2025-07-05 02:42:36.929 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 02:42:36.929 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 02:42:36.929 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 02:42:36.930 [Information] App: verboseLogging flag is: True
2025-07-05 02:42:36.934 [Information] App: Verifying real hardware requirements...
2025-07-05 02:42:36.935 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 02:42:36.935 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 02:42:36.935 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 02:42:36.936 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 02:42:36.936 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:36.936 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 02:42:36.936 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 02:42:36.937 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 02:42:36.947 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 02:42:36.948 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 02:42:36.949 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 02:42:36.951 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 02:42:36.952 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-05 02:42:36.972 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 02:42:36.973 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-05 02:42:36.973 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 02:42:36.974 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 02:42:36.974 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 02:42:37.029 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 02:42:37.031 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 02:42:37.031 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 02:42:37.031 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 02:42:37.031 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 02:42:37.031 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 02:42:37.033 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 02:42:37.039 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 02:42:37.042 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 02:42:37.043 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 02:42:37.043 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 02:42:37.057 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll
2025-07-05 02:42:37.059 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 02:42:37.061 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apcidb.dll
2025-07-05 02:42:37.062 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 02:42:37.065 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-05 02:42:37.069 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 02:42:37.070 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-05 02:42:37.072 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 02:42:37.073 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-05 02:42:37.074 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusTea2Data.dll
2025-07-05 02:42:37.075 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 02:42:37.077 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 02:42:37.078 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 02:42:37.080 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 02:42:37.081 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-05 02:42:37.082 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Core.dll
2025-07-05 02:42:37.083 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-05 02:42:37.084 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Logging.dll
2025-07-05 02:42:37.085 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-05 02:42:37.085 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.dll
2025-07-05 02:42:37.087 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 02:42:37.088 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 02:42:37.089 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-05 02:42:37.090 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Baf.Utility.dll
2025-07-05 02:42:37.091 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 02:42:37.092 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 02:42:37.093 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-05 02:42:37.094 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.ServiceContract.dll
2025-07-05 02:42:37.096 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-05 02:42:37.098 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.Utility.dll
2025-07-05 02:42:37.099 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-05 02:42:37.100 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll.config
2025-07-05 02:42:37.101 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-05 02:42:37.102 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll.config
2025-07-05 02:42:37.106 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-05 02:42:37.110 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.dll
2025-07-05 02:42:37.111 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-05 02:42:37.112 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.Caches.SysCache2.dll
2025-07-05 02:42:37.113 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-05 02:42:37.114 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Iesi.Collections.dll
2025-07-05 02:42:37.115 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-05 02:42:37.116 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Ionic.Zip.Reduced.dll
2025-07-05 02:42:37.117 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-05 02:42:37.119 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-05 02:42:37.120 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\DotNetZip.dll
2025-07-05 02:42:37.121 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-05 02:42:37.123 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\ICSharpCode.SharpZipLib.dll
2025-07-05 02:42:37.124 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-05 02:42:37.125 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.CommonDomain.Model.dll
2025-07-05 02:42:37.126 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-05 02:42:37.127 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.Contracts.Common.dll
2025-07-05 02:42:37.128 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-05 02:42:37.129 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.UtilityComponent.dll
2025-07-05 02:42:37.130 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-05 02:42:37.132 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\log4net.dll
2025-07-05 02:42:37.134 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-05 02:42:37.135 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-05 02:42:37.136 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\AutoMapper.dll
2025-07-05 02:42:37.140 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 02:42:37.142 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-07-05 02:42:37.143 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.AppContext.dll
2025-07-05 02:42:37.144 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-07-05 02:42:37.146 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Buffers.dll
2025-07-05 02:42:37.147 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-05 02:42:37.148 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Concurrent.dll
2025-07-05 02:42:37.150 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.dll
2025-07-05 02:42:37.151 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.dll
2025-07-05 02:42:37.152 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-05 02:42:37.153 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.NonGeneric.dll
2025-07-05 02:42:37.154 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-05 02:42:37.155 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Specialized.dll
2025-07-05 02:42:37.156 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-05 02:42:37.158 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.dll
2025-07-05 02:42:37.159 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-05 02:42:37.159 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.EventBasedAsync.dll
2025-07-05 02:42:37.160 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-05 02:42:37.160 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.Primitives.dll
2025-07-05 02:42:37.161 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-05 02:42:37.162 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.TypeConverter.dll
2025-07-05 02:42:37.163 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Console.dll
2025-07-05 02:42:37.164 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Console.dll
2025-07-05 02:42:37.165 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-05 02:42:37.166 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.Common.dll
2025-07-05 02:42:37.169 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-05 02:42:37.171 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SQLite.dll
2025-07-05 02:42:37.172 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-05 02:42:37.174 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SqlServerCe.dll
2025-07-05 02:42:37.175 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-05 02:42:37.176 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Contracts.dll
2025-07-05 02:42:37.177 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-05 02:42:37.178 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Debug.dll
2025-07-05 02:42:37.178 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-05 02:42:37.179 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.FileVersionInfo.dll
2025-07-05 02:42:37.180 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-05 02:42:37.181 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Process.dll
2025-07-05 02:42:37.182 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-05 02:42:37.183 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.StackTrace.dll
2025-07-05 02:42:37.185 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 02:42:37.186 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 02:42:37.187 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-05 02:42:37.188 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tools.dll
2025-07-05 02:42:37.189 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-05 02:42:37.190 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TraceSource.dll
2025-07-05 02:42:37.192 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-05 02:42:37.192 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tracing.dll
2025-07-05 02:42:37.193 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-05 02:42:37.194 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Drawing.Primitives.dll
2025-07-05 02:42:37.196 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-05 02:42:37.197 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Dynamic.Runtime.dll
2025-07-05 02:42:37.197 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-05 02:42:37.198 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Calendars.dll
2025-07-05 02:42:37.199 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-07-05 02:42:37.200 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.dll
2025-07-05 02:42:37.201 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-05 02:42:37.202 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Extensions.dll
2025-07-05 02:42:37.203 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-05 02:42:37.204 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.dll
2025-07-05 02:42:37.206 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-05 02:42:37.207 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.ZipFile.dll
2025-07-05 02:42:37.208 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.dll
2025-07-05 02:42:37.209 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.dll
2025-07-05 02:42:37.210 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-05 02:42:37.211 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.dll
2025-07-05 02:42:37.212 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-05 02:42:37.213 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.DriveInfo.dll
2025-07-05 02:42:37.214 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-05 02:42:37.215 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Primitives.dll
2025-07-05 02:42:37.216 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-05 02:42:37.216 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Watcher.dll
2025-07-05 02:42:37.217 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-05 02:42:37.218 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.IsolatedStorage.dll
2025-07-05 02:42:37.219 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-05 02:42:37.220 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.MemoryMappedFiles.dll
2025-07-05 02:42:37.221 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-05 02:42:37.223 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Pipes.dll
2025-07-05 02:42:37.224 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-05 02:42:37.225 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.UnmanagedMemoryStream.dll
2025-07-05 02:42:37.226 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.dll
2025-07-05 02:42:37.227 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.dll
2025-07-05 02:42:37.228 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-05 02:42:37.229 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Expressions.dll
2025-07-05 02:42:37.230 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-05 02:42:37.231 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Parallel.dll
2025-07-05 02:42:37.232 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-05 02:42:37.232 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Queryable.dll
2025-07-05 02:42:37.233 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Memory.dll
2025-07-05 02:42:37.234 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Memory.dll
2025-07-05 02:42:37.235 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-05 02:42:37.237 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Http.dll
2025-07-05 02:42:37.238 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-05 02:42:37.239 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NameResolution.dll
2025-07-05 02:42:37.240 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-05 02:42:37.241 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NetworkInformation.dll
2025-07-05 02:42:37.242 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-05 02:42:37.243 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Ping.dll
2025-07-05 02:42:37.244 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-05 02:42:37.245 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Primitives.dll
2025-07-05 02:42:37.246 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-05 02:42:37.247 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Requests.dll
2025-07-05 02:42:37.247 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-05 02:42:37.248 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Security.dll
2025-07-05 02:42:37.249 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-05 02:42:37.250 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Sockets.dll
2025-07-05 02:42:37.250 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-05 02:42:37.251 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebHeaderCollection.dll
2025-07-05 02:42:37.252 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-05 02:42:37.253 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.Client.dll
2025-07-05 02:42:37.254 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-05 02:42:37.255 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.dll
2025-07-05 02:42:37.256 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-05 02:42:37.258 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Numerics.Vectors.dll
2025-07-05 02:42:37.259 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-05 02:42:37.260 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.ObjectModel.dll
2025-07-05 02:42:37.261 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-07-05 02:42:37.262 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.dll
2025-07-05 02:42:37.263 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-05 02:42:37.264 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Extensions.dll
2025-07-05 02:42:37.265 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-05 02:42:37.266 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Primitives.dll
2025-07-05 02:42:37.267 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-05 02:42:37.268 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Reader.dll
2025-07-05 02:42:37.269 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-05 02:42:37.270 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.ResourceManager.dll
2025-07-05 02:42:37.271 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-05 02:42:37.273 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Writer.dll
2025-07-05 02:42:37.274 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 02:42:37.275 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 02:42:37.276 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 02:42:37.277 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 02:42:37.278 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-07-05 02:42:37.279 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.dll
2025-07-05 02:42:37.280 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-05 02:42:37.281 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Extensions.dll
2025-07-05 02:42:37.284 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-05 02:42:37.286 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Handles.dll
2025-07-05 02:42:37.287 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-05 02:42:37.288 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.dll
2025-07-05 02:42:37.289 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 02:42:37.291 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 02:42:37.292 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-05 02:42:37.294 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Numerics.dll
2025-07-05 02:42:37.295 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-05 02:42:37.296 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Formatters.dll
2025-07-05 02:42:37.297 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-05 02:42:37.298 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Json.dll
2025-07-05 02:42:37.299 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-05 02:42:37.301 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Primitives.dll
2025-07-05 02:42:37.303 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-05 02:42:37.304 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Xml.dll
2025-07-05 02:42:37.305 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-05 02:42:37.306 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Claims.dll
2025-07-05 02:42:37.308 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-05 02:42:37.309 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Algorithms.dll
2025-07-05 02:42:37.310 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-05 02:42:37.311 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Csp.dll
2025-07-05 02:42:37.312 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-05 02:42:37.313 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Encoding.dll
2025-07-05 02:42:37.314 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-05 02:42:37.315 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Primitives.dll
2025-07-05 02:42:37.316 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-05 02:42:37.319 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.X509Certificates.dll
2025-07-05 02:42:37.320 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-05 02:42:37.321 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Principal.dll
2025-07-05 02:42:37.322 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-05 02:42:37.323 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.SecureString.dll
2025-07-05 02:42:37.324 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-05 02:42:37.325 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.dll
2025-07-05 02:42:37.326 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-05 02:42:37.327 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.Extensions.dll
2025-07-05 02:42:37.328 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-05 02:42:37.329 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.RegularExpressions.dll
2025-07-05 02:42:37.330 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.dll
2025-07-05 02:42:37.331 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.dll
2025-07-05 02:42:37.338 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-05 02:42:37.341 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Overlapped.dll
2025-07-05 02:42:37.343 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-05 02:42:37.344 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.dll
2025-07-05 02:42:37.345 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-05 02:42:37.346 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Extensions.dll
2025-07-05 02:42:37.347 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-05 02:42:37.348 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Parallel.dll
2025-07-05 02:42:37.350 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-05 02:42:37.351 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Thread.dll
2025-07-05 02:42:37.352 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-05 02:42:37.353 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.ThreadPool.dll
2025-07-05 02:42:37.355 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-05 02:42:37.357 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Timer.dll
2025-07-05 02:42:37.358 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-05 02:42:37.359 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.ValueTuple.dll
2025-07-05 02:42:37.360 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-05 02:42:37.361 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.ReaderWriter.dll
2025-07-05 02:42:37.362 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-05 02:42:37.363 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XDocument.dll
2025-07-05 02:42:37.364 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-05 02:42:37.365 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlDocument.dll
2025-07-05 02:42:37.366 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-05 02:42:37.367 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlSerializer.dll
2025-07-05 02:42:37.368 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-05 02:42:37.369 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.dll
2025-07-05 02:42:37.370 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-05 02:42:37.371 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.XDocument.dll
2025-07-05 02:42:37.372 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-07-05 02:42:37.374 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\SystemInterface.dll
2025-07-05 02:42:37.374 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 02:42:37.379 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 02:42:37.379 [Information] PhoenixVocomAdapter: Current process architecture: x86
2025-07-05 02:42:37.462 [Information] PhoenixVocomAdapter: Found compatible APCI library at: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 02:42:37.466 [Information] PhoenixVocomAdapter: Successfully loaded APCI library from: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 02:42:37.468 [Warning] PhoenixVocomAdapter: No APCI initialize function found in library
2025-07-05 02:42:37.468 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 02:42:37.469 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 02:42:37.473 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 02:42:37.473 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 02:42:37.474 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-05 02:42:37.474 [Information] VocomDiagnosticTool: Successfully loaded apci.dll
2025-07-05 02:42:37.474 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_Initialize
2025-07-05 02:42:37.474 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_Shutdown
2025-07-05 02:42:37.474 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_DetectDevices
2025-07-05 02:42:37.475 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_ConnectDevice
2025-07-05 02:42:37.475 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_DisconnectDevice
2025-07-05 02:42:37.475 [Warning] VocomDiagnosticTool: ✗ Missing function: APCI_SendData
2025-07-05 02:42:37.475 [Warning] VocomDiagnosticTool: ✗ Missing function: ApciInitialize
2025-07-05 02:42:37.475 [Warning] VocomDiagnosticTool: ✗ Missing function: ApciShutdown
2025-07-05 02:42:37.481 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 02:42:37.481 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:37.482 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 02:42:37.482 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 02:42:37.485 [Error] VocomDiagnosticTool: Failed to load WUDFPuma.dll. Error: 0 (0x0)
2025-07-05 02:42:37.486 [Error] VocomDiagnosticTool: Unknown error code: 0
2025-07-05 02:42:37.487 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 02:42:37.487 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-05 02:42:37.487 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-05 02:42:37.488 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 02:42:37.488 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 02:42:37.489 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 02:42:37.489 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 02:42:37.489 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 02:42:37.490 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 02:42:37.490 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 02:42:37.492 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 02:42:37.493 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 02:42:37.494 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 02:42:37.514 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 02:42:37.514 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 02:42:37.514 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 02:42:37.515 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 02:42:37.521 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 02:42:37.522 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: apci.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:37.578 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:37.858 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Rpci.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:37.969 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Pc2.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:38.049 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:38.129 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:38.207 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:38.365 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:38.479 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:38.480 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:38.718 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixESW.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 02:42:38.805 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:38.967 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:39.097 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:39.107 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:39.162 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 02:42:39.164 [Information] VocomNativeInterop_Patch: Loading function pointers from Vocom driver DLL
2025-07-05 02:42:39.164 [Error] VocomNativeInterop_Patch: Failed to find any initialize function in the DLL
2025-07-05 02:42:39.165 [Error] VocomNativeInterop_Patch: Failed to load function pointers from Vocom driver DLL
2025-07-05 02:42:39.166 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 02:42:39.166 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 02:42:39.167 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 02:42:39.168 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 02:42:39.180 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 02:42:39.180 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:39.180 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:39.181 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:39.182 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 02:42:39.183 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 02:42:39.184 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 02:42:39.184 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 02:42:39.185 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 02:42:39.185 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 02:42:39.185 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 02:42:39.203 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WdfCoInstaller01009.dll
2025-07-05 02:42:39.225 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WUDFUpdate_01009.dll
2025-07-05 02:42:39.238 [Warning] WUDFPumaDependencyResolver: Could not load dependency: winusbcoinstaller2.dll
2025-07-05 02:42:39.238 [Error] WUDFPumaDependencyResolver: Failed to load WUDFPuma.dll. Error code: 0 (0x0)
2025-07-05 02:42:39.239 [Error] WUDFPumaDependencyResolver: Error details: Unknown error code: 0
2025-07-05 02:42:39.239 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-05 02:42:39.239 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-05 02:42:39.252 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-05 02:42:39.280 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-05 02:42:39.287 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-05 02:42:39.288 [Information] VocomNativeInterop: No serial ports found
2025-07-05 02:42:39.288 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-05 02:42:39.289 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-05 02:42:39.289 [Error] VocomDriver: Failed to initialize Vocom native interop
2025-07-05 02:42:39.290 [Warning] PatchedVocomServiceFactory: Failed to initialize standard Vocom driver, falling back to device driver
2025-07-05 02:42:39.290 [Information] VocomDeviceDriver: Initializing Vocom device driver
2025-07-05 02:42:39.291 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 02:42:39.291 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 02:42:39.292 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:39.292 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:39.292 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 02:42:39.292 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 02:42:39.293 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 02:42:39.293 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 02:42:39.294 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 02:42:39.294 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 02:42:39.294 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 02:42:39.295 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 02:42:39.297 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WdfCoInstaller01009.dll
2025-07-05 02:42:39.299 [Warning] WUDFPumaDependencyResolver: Could not load dependency: WUDFUpdate_01009.dll
2025-07-05 02:42:39.301 [Warning] WUDFPumaDependencyResolver: Could not load dependency: winusbcoinstaller2.dll
2025-07-05 02:42:39.301 [Error] WUDFPumaDependencyResolver: Failed to load WUDFPuma.dll. Error code: 0 (0x0)
2025-07-05 02:42:39.301 [Error] WUDFPumaDependencyResolver: Error details: Unknown error code: 0
2025-07-05 02:42:39.301 [Warning] VocomNativeInterop: Failed to load WUDFPuma.dll or function pointers, falling back to modern .NET 8.0 implementation
2025-07-05 02:42:39.302 [Information] VocomNativeInterop: Using modern .NET 8.0 implementation for Vocom communication
2025-07-05 02:42:39.302 [Information] VocomNativeInterop: Initializing HID device detection for Vocom adapters
2025-07-05 02:42:39.302 [Information] VocomNativeInterop: No HID Vocom devices found
2025-07-05 02:42:39.303 [Information] VocomNativeInterop: Initializing serial port detection for Vocom adapters
2025-07-05 02:42:39.303 [Information] VocomNativeInterop: No serial ports found
2025-07-05 02:42:39.303 [Warning] VocomNativeInterop: Failed to initialize modern Vocom communication, no devices found
2025-07-05 02:42:39.303 [Error] VocomNativeInterop: Failed to initialize Vocom driver
2025-07-05 02:42:39.304 [Error] VocomDeviceDriver: Failed to initialize Vocom native interop layer
2025-07-05 02:42:39.304 [Error] PatchedVocomServiceFactory: Failed to initialize Vocom device driver, falling back to dummy implementation
2025-07-05 02:42:39.307 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 02:42:39.307 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 02:42:39.308 [Information] App: Initializing Vocom service
2025-07-05 02:42:39.308 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 02:42:39.308 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 02:42:39.309 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 02:42:39.411 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 02:42:39.411 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 02:42:39.412 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 02:42:39.612 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 02:42:39.613 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-05 02:42:39.621 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 02:42:39.624 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:39.624 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 02:42:39.626 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 02:42:39.629 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 02:42:39.629 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 02:42:39.634 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 02:42:39.640 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 02:42:39.644 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 02:42:39.655 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 02:42:39.658 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 02:42:39.671 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 02:42:39.673 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 02:42:39.685 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 02:42:39.686 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 02:42:39.686 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 02:42:39.687 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 02:42:39.687 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 02:42:39.687 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 02:42:39.687 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 02:42:39.688 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 02:42:39.688 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 02:42:39.689 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 02:42:39.689 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 02:42:39.689 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 02:42:39.690 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 02:42:39.690 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 02:42:39.690 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 02:42:39.690 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 02:42:39.690 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 02:42:39.692 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 02:42:39.699 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 02:42:39.700 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 02:42:39.715 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 02:42:39.717 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.723 [Information] CANRegisterAccess: Read value 0x18 from register 0x0141 (simulated)
2025-07-05 02:42:39.730 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.736 [Information] CANRegisterAccess: Read value 0x73 from register 0x0141 (simulated)
2025-07-05 02:42:39.736 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 02:42:39.737 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 02:42:39.737 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 02:42:39.742 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 02:42:39.743 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 02:42:39.748 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 02:42:39.749 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 02:42:39.749 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 02:42:39.755 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 02:42:39.756 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 02:42:39.756 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 02:42:39.762 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 02:42:39.763 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 02:42:39.768 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 02:42:39.769 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 02:42:39.774 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 02:42:39.775 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 02:42:39.780 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 02:42:39.781 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 02:42:39.786 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 02:42:39.787 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 02:42:39.792 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 02:42:39.793 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 02:42:39.798 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 02:42:39.799 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 02:42:39.804 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 02:42:39.805 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 02:42:39.810 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 02:42:39.811 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 02:42:39.816 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 02:42:39.817 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 02:42:39.822 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 02:42:39.823 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 02:42:39.828 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 02:42:39.829 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 02:42:39.834 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 02:42:39.835 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 02:42:39.840 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 02:42:39.841 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 02:42:39.846 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 02:42:39.847 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 02:42:39.852 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 02:42:39.853 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 02:42:39.858 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 02:42:39.859 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 02:42:39.859 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 02:42:39.864 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 02:42:39.864 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 02:42:39.865 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 02:42:39.865 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.871 [Information] CANRegisterAccess: Read value 0x0D from register 0x0141 (simulated)
2025-07-05 02:42:39.877 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.883 [Information] CANRegisterAccess: Read value 0x11 from register 0x0141 (simulated)
2025-07-05 02:42:39.889 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.895 [Information] CANRegisterAccess: Read value 0x3D from register 0x0141 (simulated)
2025-07-05 02:42:39.901 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.907 [Information] CANRegisterAccess: Read value 0x73 from register 0x0141 (simulated)
2025-07-05 02:42:39.913 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:39.919 [Information] CANRegisterAccess: Read value 0x8C from register 0x0141 (simulated)
2025-07-05 02:42:39.919 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 02:42:39.920 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 02:42:39.920 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 02:42:39.920 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 02:42:39.926 [Information] CANRegisterAccess: Read value 0x25 from register 0x0140 (simulated)
2025-07-05 02:42:39.932 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 02:42:39.938 [Information] CANRegisterAccess: Read value 0x0C from register 0x0140 (simulated)
2025-07-05 02:42:39.944 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 02:42:39.950 [Information] CANRegisterAccess: Read value 0x9F from register 0x0140 (simulated)
2025-07-05 02:42:39.950 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 02:42:39.951 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 02:42:39.953 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 02:42:39.953 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 02:42:39.964 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 02:42:39.965 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 02:42:39.965 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 02:42:39.967 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 02:42:40.118 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 02:42:40.119 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 02:42:40.119 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 02:42:40.120 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 02:42:40.120 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 02:42:40.131 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 02:42:40.132 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 02:42:40.133 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 02:42:40.143 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 02:42:40.154 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 02:42:40.165 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 02:42:40.176 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 02:42:40.187 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 02:42:40.189 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 02:42:40.190 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 02:42:40.200 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 02:42:40.201 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 02:42:40.202 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 02:42:40.212 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 02:42:40.224 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 02:42:40.234 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 02:42:40.245 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 02:42:40.256 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 02:42:40.267 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 02:42:40.274 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 02:42:40.274 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 02:42:40.285 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 02:42:40.286 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 02:42:40.287 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 02:42:40.288 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 02:42:40.288 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 02:42:40.288 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 02:42:40.289 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 02:42:40.289 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 02:42:40.289 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 02:42:40.290 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 02:42:40.290 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 02:42:40.290 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 02:42:40.291 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 02:42:40.291 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 02:42:40.291 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 02:42:40.291 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 02:42:40.292 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 02:42:40.392 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 02:42:40.392 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 02:42:40.394 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 02:42:40.395 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:40.395 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 02:42:40.396 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 02:42:40.396 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:40.396 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 02:42:40.396 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 02:42:40.397 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:40.397 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 02:42:40.397 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 02:42:40.398 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:40.398 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 02:42:40.398 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 02:42:40.399 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 02:42:40.400 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-05 02:42:40.400 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-05 02:42:40.412 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 02:42:40.413 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 02:42:40.416 [Information] BackupService: Initializing backup service
2025-07-05 02:42:40.416 [Information] BackupService: Backup service initialized successfully
2025-07-05 02:42:40.416 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 02:42:40.417 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 02:42:40.419 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 02:42:40.456 [Information] BackupService: Compressing backup data
2025-07-05 02:42:40.472 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-05 02:42:40.473 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 02:42:40.473 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 02:42:40.473 [Information] BackupService: Compressing backup data
2025-07-05 02:42:40.475 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-07-05 02:42:40.475 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 02:42:40.476 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 02:42:40.476 [Information] BackupService: Compressing backup data
2025-07-05 02:42:40.477 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-05 02:42:40.477 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 02:42:40.478 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 02:42:40.478 [Information] BackupService: Compressing backup data
2025-07-05 02:42:40.479 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-05 02:42:40.480 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 02:42:40.480 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 02:42:40.487 [Information] BackupService: Compressing backup data
2025-07-05 02:42:40.489 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-07-05 02:42:40.489 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 02:42:40.490 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 02:42:40.498 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 02:42:40.499 [Information] BackupService: Compressing backup data
2025-07-05 02:42:40.501 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (516 bytes)
2025-07-05 02:42:40.501 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 02:42:40.502 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 02:42:40.503 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 02:42:40.506 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 02:42:40.535 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 02:42:40.653 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 02:42:40.654 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 02:42:40.656 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 02:42:40.656 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 02:42:40.656 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 02:42:40.658 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 02:42:40.659 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 02:42:40.664 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 02:42:40.665 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 02:42:40.680 [Information] LicensingService: Initializing licensing service
2025-07-05 02:42:40.756 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-05 02:42:40.759 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 02:42:40.759 [Information] App: Licensing service initialized successfully
2025-07-05 02:42:40.760 [Information] App: License status: Trial
2025-07-05 02:42:40.760 [Information] App: Trial period: 30 days remaining
2025-07-05 02:42:40.761 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 02:42:40.786 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-05 02:42:40.944 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 02:42:40.945 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 02:42:40.995 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 02:42:40.996 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 02:42:40.996 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 02:42:40.996 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 02:42:40.996 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 02:42:40.997 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 02:42:40.998 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 02:42:40.999 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 02:42:40.999 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 02:42:40.999 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 02:42:41.010 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 02:42:41.011 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 02:42:41.011 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 02:42:41.011 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 02:42:41.011 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 02:42:41.012 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 02:42:41.012 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 02:42:41.012 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 02:42:41.012 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 02:42:41.013 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 02:42:41.013 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 02:42:41.013 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 02:42:41.013 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 02:42:41.014 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 02:42:41.014 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 02:42:41.014 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 02:42:41.014 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 02:42:41.015 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 02:42:41.020 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 02:42:41.021 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 02:42:41.021 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 02:42:41.021 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:41.027 [Information] CANRegisterAccess: Read value 0xF4 from register 0x0141 (simulated)
2025-07-05 02:42:41.033 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:41.039 [Information] CANRegisterAccess: Read value 0x31 from register 0x0141 (simulated)
2025-07-05 02:42:41.039 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 02:42:41.040 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 02:42:41.040 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 02:42:41.046 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 02:42:41.047 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 02:42:41.052 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 02:42:41.053 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 02:42:41.053 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 02:42:41.059 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 02:42:41.059 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 02:42:41.060 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 02:42:41.066 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 02:42:41.067 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 02:42:41.073 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 02:42:41.074 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 02:42:41.080 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 02:42:41.080 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 02:42:41.086 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 02:42:41.086 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 02:42:41.092 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 02:42:41.092 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 02:42:41.098 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 02:42:41.098 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 02:42:41.104 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 02:42:41.104 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 02:42:41.110 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 02:42:41.110 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 02:42:41.116 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 02:42:41.116 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 02:42:41.122 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 02:42:41.122 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 02:42:41.129 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 02:42:41.129 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 02:42:41.135 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 02:42:41.135 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 02:42:41.141 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 02:42:41.141 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 02:42:41.147 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 02:42:41.147 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 02:42:41.153 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 02:42:41.153 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 02:42:41.159 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 02:42:41.159 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 02:42:41.165 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 02:42:41.165 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 02:42:41.166 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 02:42:41.172 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 02:42:41.173 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 02:42:41.173 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 02:42:41.173 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 02:42:41.179 [Information] CANRegisterAccess: Read value 0xB2 from register 0x0141 (simulated)
2025-07-05 02:42:41.179 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 02:42:41.180 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 02:42:41.180 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 02:42:41.180 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 02:42:41.186 [Information] CANRegisterAccess: Read value 0x7F from register 0x0140 (simulated)
2025-07-05 02:42:41.186 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 02:42:41.187 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 02:42:41.187 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 02:42:41.187 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 02:42:41.198 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 02:42:41.198 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 02:42:41.198 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 02:42:41.198 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 02:42:41.349 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 02:42:41.350 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 02:42:41.350 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 02:42:41.350 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 02:42:41.351 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 02:42:41.361 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 02:42:41.362 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 02:42:41.362 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 02:42:41.372 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 02:42:41.383 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 02:42:41.394 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 02:42:41.405 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 02:42:41.416 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 02:42:41.417 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 02:42:41.417 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 02:42:41.428 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 02:42:41.428 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 02:42:41.429 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 02:42:41.440 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 02:42:41.451 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 02:42:41.462 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 02:42:41.473 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 02:42:41.484 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 02:42:41.495 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 02:42:41.495 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 02:42:41.496 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 02:42:41.507 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 02:42:41.507 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 02:42:41.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 02:42:41.508 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 02:42:41.508 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 02:42:41.508 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 02:42:41.508 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 02:42:41.509 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 02:42:41.509 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 02:42:41.509 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 02:42:41.509 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 02:42:41.509 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 02:42:41.510 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 02:42:41.510 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 02:42:41.510 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 02:42:41.510 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 02:42:41.510 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 02:42:41.611 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 02:42:41.611 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 02:42:41.611 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 02:42:41.612 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:41.612 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 02:42:41.612 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 02:42:41.612 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:41.613 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 02:42:41.613 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 02:42:41.613 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:41.613 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 02:42:41.614 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 02:42:41.614 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 02:42:41.614 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 02:42:41.614 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 02:42:41.665 [Information] BackupService: Initializing backup service
2025-07-05 02:42:41.666 [Information] BackupService: Backup service initialized successfully
2025-07-05 02:42:41.716 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 02:42:41.717 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 02:42:41.718 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\New folder\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 02:42:41.718 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 02:42:41.770 [Information] BackupService: Getting predefined backup categories
2025-07-05 02:42:41.822 [Information] MainViewModel: Services initialized successfully
2025-07-05 02:42:41.830 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 02:42:41.830 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 02:42:41.935 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 02:42:41.936 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-05 02:42:49.861 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 02:42:49.862 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 02:42:49.967 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 02:42:49.968 [Information] MainViewModel: Found 1 Vocom device(s)
