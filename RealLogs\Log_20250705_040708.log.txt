Log started at 7/5/2025 4:07:08 AM
2025-07-05 04:07:08.640 [Information] LoggingService: Logging service initialized
2025-07-05 04:07:08.649 [Information] App: Starting integrated application initialization
2025-07-05 04:07:08.651 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 04:07:08.655 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 04:07:08.656 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 04:07:08.656 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 04:07:08.658 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 04:07:08.658 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 04:07:08.660 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 04:07:08.797 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 04:07:08.912 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 04:07:08.941 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 04:07:08.957 [Information] VCRedistBundler: Copied api-ms-win-crt-runtime-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:07:08.985 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 04:07:09.001 [Information] VCRedistBundler: Copied api-ms-win-crt-heap-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 04:07:09.032 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 04:07:09.047 [Information] VCRedistBundler: Copied api-ms-win-crt-string-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-string-l1-1-0.dll
2025-07-05 04:07:09.049 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 04:07:09.050 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 04:07:09.051 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 04:07:09.058 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 04:07:09.058 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 04:07:09.064 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 04:07:09.064 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 04:07:09.065 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 04:07:09.076 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 04:07:09.076 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 04:07:09.081 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 04:07:09.081 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:07:09.085 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:07:09.085 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 04:07:09.089 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 04:07:09.089 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 04:07:09.092 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 04:07:09.096 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-05 04:07:09.097 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-05 04:07:09.100 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-05 04:07:09.101 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-05 04:07:09.102 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 04:07:09.103 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 04:07:09.106 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 04:07:09.107 [Information] LibraryExtractor: Copying system libraries
2025-07-05 04:07:09.116 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 04:07:09.134 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 04:07:27.676 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 04:07:45.255 [Warning] LibraryExtractor: Failed to download and extract redistributable for msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_c0f0a9bc-b592-4374-9589-9682d273c14b.exe' is denied.
2025-07-05 04:07:45.256 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 04:08:08.872 [Warning] LibraryExtractor: Failed to download and extract redistributable for msvcp140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_2001a733-7ad5-4f6e-87cf-25446affc07e.exe' is denied.
2025-07-05 04:08:08.875 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 04:08:08.876 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 04:08:08.876 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 04:08:08.876 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 04:08:08.877 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 04:08:08.877 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 04:08:08.879 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 04:08:08.881 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 04:08:08.882 [Information] DependencyManager: Initializing dependency manager
2025-07-05 04:08:08.882 [Information] DependencyManager: Setting up library search paths
2025-07-05 04:08:08.883 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 04:08:08.883 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 04:08:08.884 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 04:08:08.884 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 04:08:08.885 [Information] DependencyManager: Verifying required directories
2025-07-05 04:08:08.885 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 04:08:08.885 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 04:08:08.886 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 04:08:08.886 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 04:08:08.887 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 04:08:08.892 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-05 04:08:08.893 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-05 04:08:08.896 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-05 04:08:08.897 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-05 04:08:08.897 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-05 04:08:08.898 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-05 04:08:08.899 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 04:08:08.904 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 04:08:08.904 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 04:08:08.905 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:08:08.906 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 04:08:08.906 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 04:08:08.907 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 04:08:08.911 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 04:08:08.987 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 04:08:08.987 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 04:08:09.070 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 04:08:09.071 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 04:08:09.072 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 04:08:09.128 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 04:08:09.128 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 04:08:09.176 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 04:08:09.177 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 04:08:09.177 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 04:08:09.375 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 04:08:09.376 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 04:08:09.562 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 04:08:09.562 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 04:08:09.563 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 04:08:09.652 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 04:08:09.652 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 04:08:09.742 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 04:08:09.743 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 04:08:09.743 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 04:08:09.798 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-05 04:08:09.799 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 04:08:09.799 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 04:08:09.800 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-05 04:08:09.800 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-05 04:08:09.800 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-05 04:08:09.801 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-05 04:08:09.801 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-05 04:08:09.801 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-05 04:08:09.802 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 04:08:09.802 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 04:08:09.802 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 04:08:09.803 [Information] DependencyManager: Setting up environment variables
2025-07-05 04:08:09.803 [Information] DependencyManager: Environment variables configured
2025-07-05 04:08:09.805 [Information] DependencyManager: Verifying library loading status
2025-07-05 04:08:09.921 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-05 04:08:09.921 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 04:08:09.921 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 04:08:09.923 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 04:08:09.924 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 04:08:09.927 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 04:08:09.928 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 04:08:09.929 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 04:08:09.929 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 04:08:09.930 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 04:08:09.930 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 04:08:09.930 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 04:08:09.931 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 04:08:09.931 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 04:08:09.931 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 04:08:09.931 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 04:08:09.931 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 04:08:09.932 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 04:08:09.932 [Information] App: Integrated startup completed successfully
2025-07-05 04:08:09.936 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 04:08:10.084 [Information] App: Initializing application services
2025-07-05 04:08:10.086 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 04:08:10.086 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 04:08:10.132 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 04:08:10.133 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 04:08:10.133 [Information] App: Configuration service initialized successfully
2025-07-05 04:08:10.134 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 04:08:10.134 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 04:08:10.138 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 04:08:10.139 [Information] App: Final useDummyImplementations value: False
2025-07-05 04:08:10.139 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 04:08:10.140 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-05 04:08:10.150 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 04:08:10.151 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 04:08:10.151 [Information] App: usePatchedImplementation flag is: True
2025-07-05 04:08:10.152 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 04:08:10.152 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 04:08:10.152 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 04:08:10.152 [Information] App: verboseLogging flag is: True
2025-07-05 04:08:10.153 [Information] App: Verifying real hardware requirements...
2025-07-05 04:08:10.154 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 04:08:10.154 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 04:08:10.154 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 04:08:10.155 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 04:08:10.155 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 04:08:10.156 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 04:08:10.156 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 04:08:10.157 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 04:08:10.168 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 04:08:10.170 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 04:08:10.171 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 04:08:10.172 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 04:08:10.172 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-05 04:08:10.186 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 04:08:10.187 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-05 04:08:10.187 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 04:08:10.188 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 04:08:10.188 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 04:08:10.213 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 04:08:10.217 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 04:08:10.218 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 04:08:10.218 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-05 04:08:10.218 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-05 04:08:10.219 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-05 04:08:10.220 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-05 04:08:10.220 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-05 04:08:10.220 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:08:10.221 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 04:08:10.221 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 04:08:10.221 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 04:08:10.222 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 04:08:10.224 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 04:08:10.231 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 04:08:10.232 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 04:08:10.232 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 04:08:10.238 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll
2025-07-05 04:08:10.240 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 04:08:10.242 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apcidb.dll
2025-07-05 04:08:10.243 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 04:08:10.248 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-05 04:08:10.251 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 04:08:10.254 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-05 04:08:10.255 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 04:08:10.256 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-05 04:08:10.257 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusTea2Data.dll
2025-07-05 04:08:10.260 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 04:08:10.261 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 04:08:10.263 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 04:08:10.264 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 04:08:10.265 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-05 04:08:10.266 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Core.dll
2025-07-05 04:08:10.268 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-05 04:08:10.269 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Logging.dll
2025-07-05 04:08:10.270 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-05 04:08:10.271 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.dll
2025-07-05 04:08:10.273 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 04:08:10.274 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 04:08:10.275 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-05 04:08:10.276 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Baf.Utility.dll
2025-07-05 04:08:10.278 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 04:08:10.279 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 04:08:10.281 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-05 04:08:10.282 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.ServiceContract.dll
2025-07-05 04:08:10.283 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-05 04:08:10.284 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.Utility.dll
2025-07-05 04:08:10.285 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-05 04:08:10.286 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll.config
2025-07-05 04:08:10.287 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-05 04:08:10.288 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll.config
2025-07-05 04:08:10.293 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-05 04:08:10.296 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.dll
2025-07-05 04:08:10.298 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-05 04:08:10.299 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.Caches.SysCache2.dll
2025-07-05 04:08:10.300 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-05 04:08:10.301 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Iesi.Collections.dll
2025-07-05 04:08:10.303 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-05 04:08:10.304 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Ionic.Zip.Reduced.dll
2025-07-05 04:08:10.306 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-05 04:08:10.308 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-05 04:08:10.309 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\DotNetZip.dll
2025-07-05 04:08:10.311 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-05 04:08:10.312 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\ICSharpCode.SharpZipLib.dll
2025-07-05 04:08:10.314 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-05 04:08:10.315 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.CommonDomain.Model.dll
2025-07-05 04:08:10.317 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-05 04:08:10.319 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.Contracts.Common.dll
2025-07-05 04:08:10.321 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-05 04:08:10.322 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.UtilityComponent.dll
2025-07-05 04:08:10.324 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-05 04:08:10.325 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\log4net.dll
2025-07-05 04:08:10.327 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-05 04:08:10.328 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-05 04:08:10.330 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\AutoMapper.dll
2025-07-05 04:08:10.331 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 04:08:10.332 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-07-05 04:08:10.334 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.AppContext.dll
2025-07-05 04:08:10.335 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-07-05 04:08:10.336 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Buffers.dll
2025-07-05 04:08:10.337 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-05 04:08:10.339 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Concurrent.dll
2025-07-05 04:08:10.340 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.dll
2025-07-05 04:08:10.341 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.dll
2025-07-05 04:08:10.342 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-05 04:08:10.343 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.NonGeneric.dll
2025-07-05 04:08:10.344 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-05 04:08:10.345 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Specialized.dll
2025-07-05 04:08:10.346 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-05 04:08:10.347 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.dll
2025-07-05 04:08:10.349 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-05 04:08:10.350 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.EventBasedAsync.dll
2025-07-05 04:08:10.351 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-05 04:08:10.352 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.Primitives.dll
2025-07-05 04:08:10.354 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-05 04:08:10.355 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.TypeConverter.dll
2025-07-05 04:08:10.356 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Console.dll
2025-07-05 04:08:10.357 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Console.dll
2025-07-05 04:08:10.358 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-05 04:08:10.359 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.Common.dll
2025-07-05 04:08:10.361 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-05 04:08:10.362 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SQLite.dll
2025-07-05 04:08:10.363 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-05 04:08:10.364 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SqlServerCe.dll
2025-07-05 04:08:10.366 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-05 04:08:10.367 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Contracts.dll
2025-07-05 04:08:10.369 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-05 04:08:10.369 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Debug.dll
2025-07-05 04:08:10.371 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-05 04:08:10.372 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.FileVersionInfo.dll
2025-07-05 04:08:10.373 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-05 04:08:10.374 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Process.dll
2025-07-05 04:08:10.375 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-05 04:08:10.376 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.StackTrace.dll
2025-07-05 04:08:10.377 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 04:08:10.378 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 04:08:10.379 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-05 04:08:10.380 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tools.dll
2025-07-05 04:08:10.382 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-05 04:08:10.383 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TraceSource.dll
2025-07-05 04:08:10.384 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-05 04:08:10.385 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tracing.dll
2025-07-05 04:08:10.386 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-05 04:08:10.387 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Drawing.Primitives.dll
2025-07-05 04:08:10.388 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-05 04:08:10.389 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Dynamic.Runtime.dll
2025-07-05 04:08:10.390 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-05 04:08:10.391 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Calendars.dll
2025-07-05 04:08:10.392 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-07-05 04:08:10.392 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.dll
2025-07-05 04:08:10.393 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-05 04:08:10.394 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Extensions.dll
2025-07-05 04:08:10.395 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-05 04:08:10.396 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.dll
2025-07-05 04:08:10.397 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-05 04:08:10.397 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.ZipFile.dll
2025-07-05 04:08:10.398 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.dll
2025-07-05 04:08:10.399 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.dll
2025-07-05 04:08:10.400 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-05 04:08:10.401 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.dll
2025-07-05 04:08:10.403 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-05 04:08:10.404 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.DriveInfo.dll
2025-07-05 04:08:10.406 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-05 04:08:10.407 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Primitives.dll
2025-07-05 04:08:10.408 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-05 04:08:10.409 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Watcher.dll
2025-07-05 04:08:10.410 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-05 04:08:10.410 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.IsolatedStorage.dll
2025-07-05 04:08:10.411 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-05 04:08:10.412 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.MemoryMappedFiles.dll
2025-07-05 04:08:10.413 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-05 04:08:10.414 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Pipes.dll
2025-07-05 04:08:10.415 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-05 04:08:10.416 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.UnmanagedMemoryStream.dll
2025-07-05 04:08:10.417 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.dll
2025-07-05 04:08:10.418 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.dll
2025-07-05 04:08:10.420 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-05 04:08:10.421 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Expressions.dll
2025-07-05 04:08:10.422 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-05 04:08:10.423 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Parallel.dll
2025-07-05 04:08:10.424 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-05 04:08:10.425 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Queryable.dll
2025-07-05 04:08:10.426 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Memory.dll
2025-07-05 04:08:10.427 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Memory.dll
2025-07-05 04:08:10.428 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-05 04:08:10.429 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Http.dll
2025-07-05 04:08:10.430 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-05 04:08:10.431 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NameResolution.dll
2025-07-05 04:08:10.432 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-05 04:08:10.433 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NetworkInformation.dll
2025-07-05 04:08:10.434 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-05 04:08:10.434 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Ping.dll
2025-07-05 04:08:10.436 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-05 04:08:10.438 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Primitives.dll
2025-07-05 04:08:10.439 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-05 04:08:10.440 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Requests.dll
2025-07-05 04:08:10.441 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-05 04:08:10.442 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Security.dll
2025-07-05 04:08:10.444 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-05 04:08:10.444 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Sockets.dll
2025-07-05 04:08:10.446 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-05 04:08:10.446 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebHeaderCollection.dll
2025-07-05 04:08:10.447 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-05 04:08:10.448 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.Client.dll
2025-07-05 04:08:10.449 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-05 04:08:10.450 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.dll
2025-07-05 04:08:10.451 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-05 04:08:10.452 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Numerics.Vectors.dll
2025-07-05 04:08:10.453 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-05 04:08:10.455 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ObjectModel.dll
2025-07-05 04:08:10.456 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-07-05 04:08:10.457 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.dll
2025-07-05 04:08:10.458 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-05 04:08:10.458 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Extensions.dll
2025-07-05 04:08:10.460 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-05 04:08:10.460 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Primitives.dll
2025-07-05 04:08:10.462 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-05 04:08:10.462 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Reader.dll
2025-07-05 04:08:10.463 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-05 04:08:10.464 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.ResourceManager.dll
2025-07-05 04:08:10.465 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-05 04:08:10.466 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Writer.dll
2025-07-05 04:08:10.467 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 04:08:10.467 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 04:08:10.468 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 04:08:10.469 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 04:08:10.470 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-07-05 04:08:10.470 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.dll
2025-07-05 04:08:10.471 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-05 04:08:10.473 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Extensions.dll
2025-07-05 04:08:10.475 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-05 04:08:10.476 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Handles.dll
2025-07-05 04:08:10.478 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-05 04:08:10.478 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.dll
2025-07-05 04:08:10.479 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 04:08:10.480 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 04:08:10.481 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-05 04:08:10.482 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Numerics.dll
2025-07-05 04:08:10.483 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-05 04:08:10.484 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Formatters.dll
2025-07-05 04:08:10.485 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-05 04:08:10.485 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Json.dll
2025-07-05 04:08:10.486 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-05 04:08:10.487 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Primitives.dll
2025-07-05 04:08:10.488 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-05 04:08:10.489 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Xml.dll
2025-07-05 04:08:10.490 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-05 04:08:10.490 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Claims.dll
2025-07-05 04:08:10.491 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-05 04:08:10.492 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Algorithms.dll
2025-07-05 04:08:10.493 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-05 04:08:10.494 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Csp.dll
2025-07-05 04:08:10.495 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-05 04:08:10.496 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Encoding.dll
2025-07-05 04:08:10.496 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-05 04:08:10.497 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Primitives.dll
2025-07-05 04:08:10.498 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-05 04:08:10.499 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.X509Certificates.dll
2025-07-05 04:08:10.500 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-05 04:08:10.500 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Principal.dll
2025-07-05 04:08:10.501 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-05 04:08:10.502 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.SecureString.dll
2025-07-05 04:08:10.503 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-05 04:08:10.503 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.dll
2025-07-05 04:08:10.504 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-05 04:08:10.505 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.Extensions.dll
2025-07-05 04:08:10.506 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-05 04:08:10.508 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.RegularExpressions.dll
2025-07-05 04:08:10.513 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.dll
2025-07-05 04:08:10.515 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.dll
2025-07-05 04:08:10.516 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-05 04:08:10.517 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Overlapped.dll
2025-07-05 04:08:10.519 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-05 04:08:10.520 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.dll
2025-07-05 04:08:10.521 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-05 04:08:10.522 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Extensions.dll
2025-07-05 04:08:10.523 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-05 04:08:10.524 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Parallel.dll
2025-07-05 04:08:10.525 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-05 04:08:10.526 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Thread.dll
2025-07-05 04:08:10.528 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-05 04:08:10.529 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.ThreadPool.dll
2025-07-05 04:08:10.530 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-05 04:08:10.531 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Timer.dll
2025-07-05 04:08:10.532 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-05 04:08:10.533 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ValueTuple.dll
2025-07-05 04:08:10.535 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-05 04:08:10.535 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.ReaderWriter.dll
2025-07-05 04:08:10.537 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-05 04:08:10.537 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XDocument.dll
2025-07-05 04:08:10.538 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-05 04:08:10.539 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlDocument.dll
2025-07-05 04:08:10.541 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-05 04:08:10.541 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlSerializer.dll
2025-07-05 04:08:10.542 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-05 04:08:10.543 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.dll
2025-07-05 04:08:10.544 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-05 04:08:10.545 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.XDocument.dll
2025-07-05 04:08:10.548 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-07-05 04:08:10.549 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\SystemInterface.dll
2025-07-05 04:08:10.549 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 04:08:10.550 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 04:08:10.550 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-05 04:08:10.622 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 04:08:10.623 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-05 04:08:10.623 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 04:08:10.623 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-05 04:08:10.729 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 04:08:10.729 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-05 04:08:10.730 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-05 04:08:10.730 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 04:08:10.731 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 04:08:10.732 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 04:08:10.733 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 04:08:10.733 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-05 04:08:10.736 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-05 04:08:10.738 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 04:08:10.739 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 04:08:10.740 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 04:08:10.740 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 04:08:10.742 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-05 04:08:10.743 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-05 04:08:10.743 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-05 04:08:10.743 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-05 04:08:10.744 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-05 04:08:10.744 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-05 04:08:10.744 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-05 04:08:10.745 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 04:08:10.746 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-07-05 04:08:10.747 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-07-05 04:08:10.748 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 04:08:10.748 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 04:08:10.748 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 04:08:10.748 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:08:10.749 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-05 04:08:10.750 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-05 04:08:10.750 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 04:08:10.751 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-05 04:08:10.751 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 04:08:10.752 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-05 04:08:10.752 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 04:08:10.752 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 04:08:10.755 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 04:08:10.757 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 04:08:10.758 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 04:08:10.768 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 04:08:10.769 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 04:08:10.769 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 04:08:10.769 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 04:08:10.771 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 04:08:10.773 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:10.774 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:10.775 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-05 04:08:10.845 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:10.846 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:10.846 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-05 04:08:11.012 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:11.013 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-05 04:08:11.106 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:11.107 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-05 04:08:11.204 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:11.205 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:11.206 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-05 04:08:11.279 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:11.351 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:11.351 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-05 04:08:11.411 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 04:08:11.521 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 04:08:11.616 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:11.720 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:11.721 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-05 04:08:11.721 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 04:08:11.941 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:11.941 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-05 04:08:12.019 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:12.101 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:12.102 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-05 04:08:12.243 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 04:08:12.376 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 04:08:12.376 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-05 04:08:12.519 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 04:08:12.530 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 04:08:12.583 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 04:08:12.585 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-05 04:08:12.585 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 04:08:12.586 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-05 04:08:12.586 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 04:08:12.586 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 04:08:12.588 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 04:08:12.589 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 04:08:12.591 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 04:08:12.591 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 04:08:12.591 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 04:08:12.592 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 04:08:12.592 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 04:08:12.594 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-05 04:08:12.595 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-05 04:08:12.595 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 04:08:12.596 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 04:08:12.597 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 04:08:12.597 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 04:08:12.599 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-05 04:08:12.602 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-05 04:08:12.603 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-05 04:08:12.603 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-05 04:08:12.604 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-05 04:08:12.605 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-05 04:08:12.606 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-05 04:08:12.606 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-05 04:08:12.607 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-05 04:08:12.607 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-05 04:08:12.607 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-05 04:08:12.607 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-05 04:08:12.608 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-05 04:08:12.608 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-05 04:08:12.608 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-05 04:08:12.609 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-05 04:08:12.609 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-05 04:08:12.609 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-05 04:08:12.610 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-05 04:08:12.610 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-05 04:08:12.610 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-05 04:08:12.610 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-05 04:08:12.611 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-05 04:08:12.611 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-05 04:08:12.611 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-05 04:08:12.611 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-05 04:08:12.612 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-05 04:08:12.612 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-05 04:08:12.612 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-05 04:08:12.612 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-05 04:08:12.613 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-05 04:08:12.613 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-05 04:08:12.613 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-05 04:08:12.613 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-05 04:08:12.614 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-05 04:08:12.614 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-05 04:08:12.614 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-05 04:08:12.614 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-05 04:08:12.615 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-05 04:08:12.615 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-05 04:08:12.615 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-05 04:08:12.615 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-05 04:08:12.616 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-05 04:08:12.616 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-05 04:08:12.616 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-05 04:08:12.616 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-05 04:08:12.617 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-05 04:08:12.617 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-05 04:08:12.617 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-05 04:08:12.618 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-05 04:08:12.619 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-05 04:08:12.620 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-05 04:08:12.620 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-05 04:08:12.621 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-05 04:08:12.621 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-05 04:08:12.621 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-05 04:08:12.621 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-05 04:08:12.625 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-05 04:08:12.625 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-05 04:08:12.626 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-05 04:08:12.627 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-05 04:08:12.677 [Information] WiFiCommunicationService: WiFi is available
2025-07-05 04:08:12.677 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-05 04:08:12.679 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-05 04:08:12.680 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-05 04:08:12.681 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-05 04:08:12.682 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-05 04:08:12.684 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 04:08:12.685 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 04:08:12.686 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 04:08:12.688 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 04:08:12.692 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 04:08:12.693 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 04:08:12.693 [Information] VocomService: Native USB communication service initialized
2025-07-05 04:08:12.694 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 04:08:12.694 [Information] VocomService: Connection recovery service initialized
2025-07-05 04:08:12.694 [Information] VocomService: Enhanced services initialization completed
2025-07-05 04:08:12.696 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:12.707 [Information] VocomService: PTT application is not running
2025-07-05 04:08:12.709 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:12.710 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:12.710 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 04:08:12.710 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-05 04:08:12.711 [Information] App: Initializing Vocom service
2025-07-05 04:08:12.711 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 04:08:12.711 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 04:08:12.711 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 04:08:12.712 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 04:08:12.712 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 04:08:12.712 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 04:08:12.713 [Information] VocomService: Native USB communication service initialized
2025-07-05 04:08:12.713 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 04:08:12.713 [Information] VocomService: Connection recovery service initialized
2025-07-05 04:08:12.713 [Information] VocomService: Enhanced services initialization completed
2025-07-05 04:08:12.713 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:12.724 [Information] VocomService: PTT application is not running
2025-07-05 04:08:12.724 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:12.725 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:12.725 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 04:08:12.727 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 04:08:12.728 [Information] VocomService: Using new enhanced device detection service
2025-07-05 04:08:12.729 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 04:08:12.730 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 04:08:13.230 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 04:08:13.230 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 04:08:13.231 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 04:08:13.232 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 04:08:13.232 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 04:08:13.233 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 04:08:13.234 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 04:08:13.236 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 04:08:13.401 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 04:08:13.403 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 04:08:13.404 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 04:08:13.405 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 04:08:13.405 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 04:08:13.405 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 04:08:13.406 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 04:08:13.406 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:13.406 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:13.408 [Debug] VocomService: Checking if WiFi is available
2025-07-05 04:08:13.409 [Debug] VocomService: WiFi is available
2025-07-05 04:08:13.409 [Information] VocomService: Found 3 Vocom devices
2025-07-05 04:08:13.410 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-05 04:08:13.412 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.412 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:13.421 [Information] VocomService: PTT application is not running
2025-07-05 04:08:13.423 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 04:08:13.424 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 04:08:13.424 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:13.434 [Information] VocomService: PTT application is not running
2025-07-05 04:08:13.435 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 04:08:13.436 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 04:08:13.436 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 04:08:13.437 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 04:08:13.437 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 04:08:13.469 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 04:08:13.469 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 04:08:13.470 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 04:08:13.471 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 04:08:13.472 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 04:08:13.473 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 04:08:13.473 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 04:08:13.473 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 04:08:13.473 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.473 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-05 04:08:13.476 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 04:08:13.478 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:13.478 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 04:08:13.480 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 04:08:13.482 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 04:08:13.482 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 04:08:13.484 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 04:08:13.485 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 04:08:13.488 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 04:08:13.490 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 04:08:13.493 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 04:08:13.498 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 04:08:13.499 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 04:08:13.499 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 04:08:13.501 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 04:08:13.502 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 04:08:13.502 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 04:08:13.503 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 04:08:13.503 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 04:08:13.503 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 04:08:13.504 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 04:08:13.505 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 04:08:13.505 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 04:08:13.506 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 04:08:13.507 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 04:08:13.507 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 04:08:13.507 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 04:08:13.509 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 04:08:13.511 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:13.511 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 04:08:13.511 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 04:08:13.512 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:13.512 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 04:08:13.512 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 04:08:13.513 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:13.513 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 04:08:13.513 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 04:08:13.513 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:13.514 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 04:08:13.514 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 04:08:13.514 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 04:08:13.516 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-05 04:08:13.517 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 04:08:13.517 [Information] VocomService: Using new enhanced device detection service
2025-07-05 04:08:13.518 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 04:08:13.518 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 04:08:13.750 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 04:08:13.750 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 04:08:13.750 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 04:08:13.750 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 04:08:13.751 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 04:08:13.751 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 04:08:13.751 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 04:08:13.752 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 04:08:13.923 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 04:08:13.923 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 04:08:13.924 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 04:08:13.924 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 04:08:13.924 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 04:08:13.925 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 04:08:13.925 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 04:08:13.925 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:13.925 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:13.926 [Debug] VocomService: Checking if WiFi is available
2025-07-05 04:08:13.926 [Debug] VocomService: WiFi is available
2025-07-05 04:08:13.926 [Information] VocomService: Found 3 Vocom devices
2025-07-05 04:08:13.927 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.927 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.927 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:13.937 [Information] VocomService: PTT application is not running
2025-07-05 04:08:13.938 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 04:08:13.938 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 04:08:13.938 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:13.947 [Information] VocomService: PTT application is not running
2025-07-05 04:08:13.947 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 04:08:13.948 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 04:08:13.948 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 04:08:13.948 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 04:08:13.948 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 04:08:13.949 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 04:08:13.949 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 04:08:13.949 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 04:08:13.949 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 04:08:13.949 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 04:08:13.950 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 04:08:13.950 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 04:08:13.950 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 04:08:13.950 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 04:08:13.951 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.951 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.951 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 04:08:13.951 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:13.951 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:13.951 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:13.961 [Information] VocomService: PTT application is not running
2025-07-05 04:08:13.962 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:13.962 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:13.963 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:13.964 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-05 04:08:14.767 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:14.767 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:14.768 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-05 04:08:14.768 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:14.768 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 04:08:14.769 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 04:08:14.770 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-05 04:08:14.770 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-05 04:08:14.773 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 04:08:14.774 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 04:08:14.776 [Information] BackupService: Initializing backup service
2025-07-05 04:08:14.776 [Information] BackupService: Backup service initialized successfully
2025-07-05 04:08:14.776 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 04:08:14.776 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 04:08:14.778 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 04:08:14.801 [Information] BackupService: Compressing backup data
2025-07-05 04:08:14.816 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-05 04:08:14.816 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 04:08:14.817 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 04:08:14.817 [Information] BackupService: Compressing backup data
2025-07-05 04:08:14.818 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-05 04:08:14.818 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 04:08:14.819 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 04:08:14.819 [Information] BackupService: Compressing backup data
2025-07-05 04:08:14.820 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-05 04:08:14.820 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 04:08:14.821 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 04:08:14.821 [Information] BackupService: Compressing backup data
2025-07-05 04:08:14.822 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-05 04:08:14.823 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 04:08:14.823 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 04:08:14.823 [Information] BackupService: Compressing backup data
2025-07-05 04:08:14.824 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-07-05 04:08:14.824 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 04:08:14.825 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 04:08:14.825 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 04:08:14.826 [Information] BackupService: Compressing backup data
2025-07-05 04:08:14.827 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-05 04:08:14.827 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 04:08:14.827 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 04:08:14.828 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 04:08:14.831 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 04:08:14.833 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 04:08:14.881 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 04:08:14.882 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 04:08:14.883 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 04:08:14.884 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 04:08:14.884 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 04:08:14.885 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 04:08:14.886 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 04:08:14.889 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 04:08:14.889 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 04:08:14.898 [Information] LicensingService: Initializing licensing service
2025-07-05 04:08:14.963 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-05 04:08:14.965 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 04:08:14.965 [Information] App: Licensing service initialized successfully
2025-07-05 04:08:14.965 [Information] App: License status: Trial
2025-07-05 04:08:14.966 [Information] App: Trial period: 30 days remaining
2025-07-05 04:08:14.966 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 04:08:15.114 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 04:08:15.114 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 04:08:15.114 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 04:08:15.115 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 04:08:15.115 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 04:08:15.115 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 04:08:15.115 [Information] VocomService: Native USB communication service initialized
2025-07-05 04:08:15.116 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 04:08:15.116 [Information] VocomService: Connection recovery service initialized
2025-07-05 04:08:15.116 [Information] VocomService: Enhanced services initialization completed
2025-07-05 04:08:15.116 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:15.124 [Information] VocomService: PTT application is not running
2025-07-05 04:08:15.124 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:15.125 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:15.125 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 04:08:15.176 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 04:08:15.176 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 04:08:15.177 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 04:08:15.177 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 04:08:15.177 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 04:08:15.178 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 04:08:15.178 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 04:08:15.179 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 04:08:15.179 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 04:08:15.179 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 04:08:15.189 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 04:08:15.190 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 04:08:15.191 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 04:08:15.191 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 04:08:15.191 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 04:08:15.192 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 04:08:15.192 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 04:08:15.192 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 04:08:15.192 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 04:08:15.193 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 04:08:15.193 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 04:08:15.194 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 04:08:15.194 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 04:08:15.194 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 04:08:15.194 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 04:08:15.194 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 04:08:15.195 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 04:08:15.197 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 04:08:15.203 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 04:08:15.204 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 04:08:15.206 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 04:08:15.207 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 04:08:15.213 [Information] CANRegisterAccess: Read value 0x59 from register 0x0141 (simulated)
2025-07-05 04:08:15.214 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 04:08:15.215 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 04:08:15.215 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 04:08:15.221 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 04:08:15.221 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 04:08:15.227 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 04:08:15.227 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 04:08:15.227 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 04:08:15.233 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 04:08:15.233 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 04:08:15.233 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 04:08:15.239 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 04:08:15.239 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 04:08:15.245 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 04:08:15.245 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 04:08:15.251 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 04:08:15.251 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 04:08:15.257 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 04:08:15.257 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 04:08:15.263 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 04:08:15.263 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 04:08:15.269 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 04:08:15.269 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 04:08:15.275 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 04:08:15.275 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 04:08:15.281 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 04:08:15.281 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 04:08:15.287 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 04:08:15.287 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 04:08:15.292 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 04:08:15.292 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 04:08:15.298 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 04:08:15.298 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 04:08:15.304 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 04:08:15.304 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 04:08:15.310 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 04:08:15.310 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 04:08:15.316 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 04:08:15.316 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 04:08:15.322 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 04:08:15.322 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 04:08:15.328 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 04:08:15.328 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 04:08:15.334 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 04:08:15.334 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 04:08:15.335 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 04:08:15.340 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 04:08:15.341 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 04:08:15.341 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 04:08:15.341 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 04:08:15.346 [Information] CANRegisterAccess: Read value 0x8D from register 0x0141 (simulated)
2025-07-05 04:08:15.353 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 04:08:15.359 [Information] CANRegisterAccess: Read value 0xBD from register 0x0141 (simulated)
2025-07-05 04:08:15.365 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 04:08:15.371 [Information] CANRegisterAccess: Read value 0x63 from register 0x0141 (simulated)
2025-07-05 04:08:15.377 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 04:08:15.383 [Information] CANRegisterAccess: Read value 0x80 from register 0x0141 (simulated)
2025-07-05 04:08:15.383 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 04:08:15.383 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 04:08:15.383 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 04:08:15.384 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 04:08:15.389 [Information] CANRegisterAccess: Read value 0xEE from register 0x0140 (simulated)
2025-07-05 04:08:15.395 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 04:08:15.400 [Information] CANRegisterAccess: Read value 0x2D from register 0x0140 (simulated)
2025-07-05 04:08:15.406 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 04:08:15.413 [Information] CANRegisterAccess: Read value 0x1D from register 0x0140 (simulated)
2025-07-05 04:08:15.413 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 04:08:15.414 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 04:08:15.414 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 04:08:15.414 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 04:08:15.424 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 04:08:15.425 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 04:08:15.426 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 04:08:15.430 [Information] VocomService: Sending data and waiting for response
2025-07-05 04:08:15.430 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-05 04:08:15.482 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-05 04:08:15.483 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 04:08:15.483 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 04:08:15.483 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 04:08:15.484 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 04:08:15.494 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 04:08:15.495 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 04:08:15.495 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 04:08:15.506 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 04:08:15.517 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 04:08:15.528 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 04:08:15.540 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 04:08:15.551 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 04:08:15.552 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 04:08:15.552 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 04:08:15.563 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 04:08:15.564 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 04:08:15.564 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 04:08:15.575 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 04:08:15.586 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 04:08:15.597 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 04:08:15.608 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 04:08:15.619 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 04:08:15.630 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 04:08:15.630 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 04:08:15.630 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 04:08:15.642 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 04:08:15.643 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 04:08:15.643 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 04:08:15.643 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 04:08:15.643 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 04:08:15.644 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 04:08:15.644 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 04:08:15.644 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 04:08:15.645 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 04:08:15.645 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 04:08:15.645 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 04:08:15.645 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 04:08:15.646 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 04:08:15.646 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 04:08:15.646 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 04:08:15.646 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 04:08:15.647 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 04:08:15.748 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 04:08:15.748 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 04:08:15.748 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 04:08:15.749 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:15.749 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 04:08:15.749 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 04:08:15.749 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:15.750 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 04:08:15.750 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 04:08:15.750 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:15.750 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 04:08:15.750 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 04:08:15.751 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 04:08:15.751 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 04:08:15.751 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 04:08:15.802 [Information] BackupService: Initializing backup service
2025-07-05 04:08:15.803 [Information] BackupService: Backup service initialized successfully
2025-07-05 04:08:15.853 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 04:08:15.854 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 04:08:15.854 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (3)\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 04:08:15.855 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 04:08:15.908 [Information] BackupService: Getting predefined backup categories
2025-07-05 04:08:15.959 [Information] MainViewModel: Services initialized successfully
2025-07-05 04:08:15.961 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 04:08:15.962 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 04:08:15.963 [Information] VocomService: Using new enhanced device detection service
2025-07-05 04:08:15.963 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 04:08:15.963 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 04:08:16.148 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 04:08:16.148 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 04:08:16.148 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 04:08:16.149 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 04:08:16.149 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 04:08:16.149 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 04:08:16.149 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 04:08:16.149 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 04:08:16.308 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 04:08:16.309 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 04:08:16.309 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 04:08:16.309 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 04:08:16.309 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 04:08:16.310 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 04:08:16.310 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 04:08:16.310 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 04:08:16.311 [Debug] VocomService: Bluetooth is enabled
2025-07-05 04:08:16.311 [Debug] VocomService: Checking if WiFi is available
2025-07-05 04:08:16.312 [Debug] VocomService: WiFi is available
2025-07-05 04:08:16.312 [Information] VocomService: Found 3 Vocom devices
2025-07-05 04:08:16.312 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-05 04:08:45.157 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 04:08:45.158 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 04:08:45.158 [Information] VocomService: Using new enhanced device detection service
2025-07-05 04:08:45.159 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 04:08:45.159 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 04:08:45.332 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 04:08:45.333 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 04:08:45.333 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 04:08:45.333 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 04:08:45.334 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 04:08:45.433 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 04:08:45.434 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 04:08:45.435 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 04:08:45.435 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-05 04:08:51.816 [Information] MainViewModel: Connecting to Vocom device 
2025-07-05 04:08:51.817 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-05 04:08:51.818 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-05 04:08:51.819 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:52.215 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-05 04:08:52.215 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-05 04:08:52.216 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 04:08:52.217 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 04:08:52.218 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 04:08:52.218 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-05 04:08:52.218 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 04:08:52.219 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 04:08:52.219 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 04:08:52.232 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:52.241 [Information] VocomService: PTT application is not running
2025-07-05 04:08:52.241 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-05 04:08:52.242 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-05 04:08:52.242 [Information] VocomService: Checking if PTT application is running
2025-07-05 04:08:52.252 [Information] VocomService: PTT application is not running
2025-07-05 04:08:52.253 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-05 04:08:52.253 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-05 04:08:52.253 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-05 04:08:52.253 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 04:08:52.254 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-05 04:08:52.254 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 04:08:52.254 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-05 04:08:52.255 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 04:08:52.255 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 04:08:52.255 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 04:08:52.255 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 04:08:52.256 [Error] VocomService: Standard USB connection failed for device 
2025-07-05 04:08:52.256 [Error] VocomService: All USB connection methods failed for device 
2025-07-05 04:08:52.256 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-05 04:08:52.257 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 
2025-07-05 04:08:52.257 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 
2025-07-05 04:08:52.257 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-05 04:08:52.258 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 
2025-07-05 04:08:52.258 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-05 04:08:52.258 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-05 04:08:52.258 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device  via USB
2025-07-05 04:08:52.259 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device  via USB
2025-07-05 04:08:52.259 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-05 04:08:52.259 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device  via USB
2025-07-05 04:08:52.260 [Error] MainViewModel: Failed to connect to Vocom device 
