Log started at 7/5/2025 2:36:22 PM
2025-07-05 14:36:22.684 [Information] LoggingService: Logging service initialized
2025-07-05 14:36:22.715 [Information] App: Starting integrated application initialization
2025-07-05 14:36:22.717 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 14:36:22.722 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 14:36:22.783 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 14:36:22.784 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 14:36:22.787 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 14:36:22.791 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 14:36:22.796 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 14:36:22.824 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 14:36:22.836 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 14:36:22.847 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 14:36:22.857 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 14:36:22.860 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 14:36:22.863 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 14:36:22.864 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 14:36:22.887 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 14:36:22.887 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 14:36:22.890 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 14:36:22.891 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 14:36:22.891 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 14:36:22.894 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 14:36:22.895 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 14:36:22.897 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 14:36:22.897 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 14:36:22.898 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 14:36:22.898 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 14:36:22.911 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 14:36:22.912 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 14:36:22.912 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 14:36:22.921 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 14:36:22.922 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 14:36:22.925 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 14:36:22.927 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 14:36:22.932 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 14:36:22.935 [Information] LibraryExtractor: Copying system libraries
2025-07-05 14:36:22.943 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 14:36:22.954 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 14:36:47.543 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 14:37:32.171 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 14:38:49.671 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 14:38:49.671 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 14:38:49.672 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 14:38:49.673 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 14:38:49.673 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 14:38:49.674 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 14:38:49.678 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 14:38:49.680 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 14:38:49.681 [Information] DependencyManager: Initializing dependency manager
2025-07-05 14:38:49.682 [Information] DependencyManager: Setting up library search paths
2025-07-05 14:38:49.683 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 14:38:49.684 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 14:38:49.684 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 14:38:49.684 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 14:38:49.686 [Information] DependencyManager: Verifying required directories
2025-07-05 14:38:49.686 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 14:38:49.687 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 14:38:49.687 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 14:38:49.687 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 14:38:49.689 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 14:38:49.702 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 14:38:49.708 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 14:38:49.711 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 14:38:49.717 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 14:38:49.723 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 14:38:49.725 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 14:38:49.726 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 14:38:49.727 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 14:38:49.729 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 14:38:49.731 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 14:38:49.849 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 14:38:49.953 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 14:38:49.955 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 14:38:49.956 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 14:38:49.958 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 14:38:49.959 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 14:38:50.312 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 14:38:50.667 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 14:38:50.669 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 14:38:50.795 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 14:38:50.915 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 14:38:50.918 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 14:38:50.919 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 14:38:50.921 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 14:38:50.922 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 14:38:50.923 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 14:38:50.924 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 14:38:50.925 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 14:38:50.926 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 14:38:50.927 [Information] DependencyManager: Setting up environment variables
2025-07-05 14:38:50.927 [Information] DependencyManager: Environment variables configured
2025-07-05 14:38:50.929 [Information] DependencyManager: Verifying library loading status
2025-07-05 14:38:51.290 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 14:38:51.290 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 14:38:51.291 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 14:38:51.293 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 14:38:51.295 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 14:38:51.299 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 14:38:51.301 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 14:38:51.301 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 14:38:51.302 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 14:38:51.303 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 14:38:51.304 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 14:38:51.304 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 14:38:51.304 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 14:38:51.305 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 14:38:51.305 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 14:38:51.306 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 14:38:51.306 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 14:38:51.307 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 14:38:51.308 [Information] App: Integrated startup completed successfully
2025-07-05 14:38:51.311 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 14:38:51.500 [Information] App: Initializing application services
2025-07-05 14:38:51.502 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 14:38:51.502 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 14:38:51.573 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 14:38:51.574 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 14:38:51.575 [Information] App: Configuration service initialized successfully
2025-07-05 14:38:51.577 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 14:38:51.578 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 14:38:51.586 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 14:38:51.586 [Information] App: Final useDummyImplementations value: False
2025-07-05 14:38:51.587 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 14:38:51.616 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 14:38:51.619 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 14:38:51.620 [Information] App: usePatchedImplementation flag is: True
2025-07-05 14:38:51.621 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 14:38:51.621 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 14:38:51.622 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 14:38:51.624 [Information] App: verboseLogging flag is: True
2025-07-05 14:38:51.627 [Information] App: Verifying real hardware requirements...
2025-07-05 14:38:51.627 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 14:38:51.628 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 14:38:51.628 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 14:38:51.629 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 14:38:51.629 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 14:38:51.630 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 14:38:51.630 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 14:38:51.631 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 14:38:51.643 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 14:38:51.643 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 14:38:51.644 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 14:38:51.646 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 14:38:51.647 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-05 14:38:51.680 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 14:38:51.681 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-05 14:38:51.682 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 14:38:51.685 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 14:38:51.685 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 14:38:51.731 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 14:38:51.735 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 14:38:51.736 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 14:38:51.736 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-05 14:38:51.737 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-05 14:38:51.739 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-05 14:38:51.740 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-05 14:38:51.741 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-05 14:38:51.741 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 14:38:51.742 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 14:38:51.742 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 14:38:51.742 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 14:38:51.744 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 14:38:51.747 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 14:38:51.752 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 14:38:51.755 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 14:38:51.755 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 14:38:51.778 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 14:38:51.812 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 14:38:51.814 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 14:38:51.814 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-05 14:38:51.815 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 14:38:51.816 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-05 14:38:51.816 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 14:38:51.816 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-05 14:38:51.917 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 14:38:51.917 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-05 14:38:51.918 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-05 14:38:51.918 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 14:38:51.919 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 14:38:51.920 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 14:38:51.921 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 14:38:51.921 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-05 14:38:51.924 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-05 14:38:51.926 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 14:38:51.927 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 14:38:51.927 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 14:38:51.927 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 14:38:51.930 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-05 14:38:51.931 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-05 14:38:51.931 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-05 14:38:51.932 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-05 14:38:51.932 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-05 14:38:51.933 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-05 14:38:51.933 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-05 14:38:51.934 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 14:38:51.935 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-05 14:38:51.935 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-05 14:38:51.937 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 14:38:51.937 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 14:38:51.938 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 14:38:51.938 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 14:38:51.939 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-05 14:38:51.942 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-05 14:38:51.943 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 14:38:51.943 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-05 14:38:51.944 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 14:38:51.945 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-05 14:38:51.945 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 14:38:51.945 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 14:38:51.948 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 14:38:51.951 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 14:38:51.951 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 14:38:51.959 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 14:38:51.959 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 14:38:51.960 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 14:38:51.960 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 14:38:51.964 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 14:38:51.966 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.967 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.968 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-05 14:38:51.968 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.969 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.969 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-05 14:38:51.971 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.971 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-05 14:38:51.973 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.974 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-05 14:38:51.975 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.976 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.976 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-05 14:38:51.977 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.979 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.979 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-05 14:38:51.981 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 14:38:51.982 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 14:38:51.983 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.984 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.984 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-05 14:38:51.985 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 14:38:51.986 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.987 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-05 14:38:51.988 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.990 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.990 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-05 14:38:51.992 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 14:38:51.993 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 14:38:51.994 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-05 14:38:51.996 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 14:38:52.187 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 14:38:52.189 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 14:38:52.196 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-05 14:38:52.196 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 14:38:52.196 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-05 14:38:52.197 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 14:38:52.198 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 14:38:52.200 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 14:38:52.202 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 14:38:52.206 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 14:38:52.207 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 14:38:52.208 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 14:38:52.209 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 14:38:52.210 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 14:38:52.212 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-05 14:38:52.213 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-05 14:38:52.216 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 14:38:52.216 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 14:38:52.217 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 14:38:52.217 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 14:38:52.222 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-05 14:38:52.227 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-05 14:38:52.230 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-05 14:38:52.231 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-05 14:38:52.232 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-05 14:38:52.233 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-05 14:38:52.234 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-05 14:38:52.235 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-05 14:38:52.235 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-05 14:38:52.236 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-05 14:38:52.236 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-05 14:38:52.236 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-05 14:38:52.237 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-05 14:38:52.237 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-05 14:38:52.237 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-05 14:38:52.238 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-05 14:38:52.238 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-05 14:38:52.238 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-05 14:38:52.239 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-05 14:38:52.239 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-05 14:38:52.239 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-05 14:38:52.241 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-05 14:38:52.241 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-05 14:38:52.242 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-05 14:38:52.242 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-05 14:38:52.243 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-05 14:38:52.243 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-05 14:38:52.243 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-05 14:38:52.244 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-05 14:38:52.244 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-05 14:38:52.244 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-05 14:38:52.245 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-05 14:38:52.245 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-05 14:38:52.245 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-05 14:38:52.246 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-05 14:38:52.246 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-05 14:38:52.246 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-05 14:38:52.247 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-05 14:38:52.247 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-05 14:38:52.247 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-05 14:38:52.247 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-05 14:38:52.248 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-05 14:38:52.248 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-05 14:38:52.248 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-05 14:38:52.249 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-05 14:38:52.249 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-05 14:38:52.249 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-05 14:38:52.249 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-05 14:38:52.250 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-05 14:38:52.251 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-05 14:38:52.252 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-05 14:38:52.253 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-05 14:38:52.254 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-05 14:38:52.254 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-05 14:38:52.254 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-05 14:38:52.255 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-05 14:38:52.255 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-05 14:38:52.260 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-05 14:38:52.262 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-05 14:38:52.263 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-05 14:38:52.265 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-05 14:38:52.374 [Information] WiFiCommunicationService: WiFi is available
2025-07-05 14:38:52.375 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-05 14:38:52.377 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-05 14:38:52.378 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-05 14:38:52.380 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-05 14:38:52.382 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-05 14:38:52.383 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 14:38:52.384 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 14:38:52.386 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 14:38:52.388 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 14:38:52.394 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 14:38:52.395 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 14:38:52.395 [Information] VocomService: Native USB communication service initialized
2025-07-05 14:38:52.395 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 14:38:52.396 [Information] VocomService: Connection recovery service initialized
2025-07-05 14:38:52.397 [Information] VocomService: Enhanced services initialization completed
2025-07-05 14:38:52.399 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:52.419 [Information] VocomService: PTT application is not running
2025-07-05 14:38:52.431 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 14:38:52.431 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-05 14:38:52.431 [Information] App: Initializing Vocom service
2025-07-05 14:38:52.432 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 14:38:52.432 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 14:38:52.432 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 14:38:52.433 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 14:38:52.433 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 14:38:52.434 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 14:38:52.434 [Information] VocomService: Native USB communication service initialized
2025-07-05 14:38:52.434 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 14:38:52.434 [Information] VocomService: Connection recovery service initialized
2025-07-05 14:38:52.435 [Information] VocomService: Enhanced services initialization completed
2025-07-05 14:38:52.435 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:52.453 [Information] VocomService: PTT application is not running
2025-07-05 14:38:52.454 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 14:38:52.458 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 14:38:52.459 [Information] VocomService: Using new enhanced device detection service
2025-07-05 14:38:52.461 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 14:38:52.464 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 14:38:52.909 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 14:38:52.911 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 14:38:52.912 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 14:38:52.915 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 14:38:52.915 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 14:38:52.917 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 14:38:52.919 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 14:38:52.922 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 14:38:53.181 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 14:38:53.183 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 14:38:53.185 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 14:38:53.186 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 14:38:53.187 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 14:38:53.187 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 14:38:53.188 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 14:38:53.195 [Information] VocomService: Found 3 Vocom devices
2025-07-05 14:38:53.195 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-05 14:38:53.200 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.200 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:53.214 [Information] VocomService: PTT application is not running
2025-07-05 14:38:53.218 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 14:38:53.218 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 14:38:53.219 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:53.233 [Information] VocomService: PTT application is not running
2025-07-05 14:38:53.233 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 14:38:53.235 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 14:38:53.236 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 14:38:53.236 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 14:38:53.236 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 14:38:53.272 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 14:38:53.273 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 14:38:53.275 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 14:38:53.277 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 14:38:53.279 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 14:38:53.279 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 14:38:53.279 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 14:38:53.280 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:38:53.280 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.280 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-05 14:38:53.283 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 14:38:53.287 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:53.288 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 14:38:53.292 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 14:38:53.295 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 14:38:53.295 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 14:38:53.297 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 14:38:53.299 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 14:38:53.306 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 14:38:53.309 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 14:38:53.313 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 14:38:53.320 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 14:38:53.322 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 14:38:53.323 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 14:38:53.327 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 14:38:53.328 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 14:38:53.328 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 14:38:53.331 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 14:38:53.331 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 14:38:53.332 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 14:38:53.334 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 14:38:53.335 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 14:38:53.335 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 14:38:53.338 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 14:38:53.338 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 14:38:53.339 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 14:38:53.339 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 14:38:53.344 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 14:38:53.346 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:53.347 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 14:38:53.347 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 14:38:53.348 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:53.348 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 14:38:53.348 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 14:38:53.349 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:53.349 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 14:38:53.349 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 14:38:53.350 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:53.350 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 14:38:53.351 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 14:38:53.351 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 14:38:53.354 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-05 14:38:53.354 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 14:38:53.354 [Information] VocomService: Using new enhanced device detection service
2025-07-05 14:38:53.355 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 14:38:53.355 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 14:38:53.620 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 14:38:53.620 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 14:38:53.620 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 14:38:53.621 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 14:38:53.621 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 14:38:53.622 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 14:38:53.622 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 14:38:53.622 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 14:38:53.880 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 14:38:53.880 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 14:38:53.881 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 14:38:53.881 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 14:38:53.882 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 14:38:53.882 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 14:38:53.882 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 14:38:53.883 [Information] VocomService: Found 3 Vocom devices
2025-07-05 14:38:53.884 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.885 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.885 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:53.905 [Information] VocomService: PTT application is not running
2025-07-05 14:38:53.905 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 14:38:53.905 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 14:38:53.906 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:53.921 [Information] VocomService: PTT application is not running
2025-07-05 14:38:53.921 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 14:38:53.921 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 14:38:53.922 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 14:38:53.922 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 14:38:53.922 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 14:38:53.923 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 14:38:53.923 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 14:38:53.923 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 14:38:53.924 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 14:38:53.924 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 14:38:53.925 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 14:38:53.925 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 14:38:53.925 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:38:53.926 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:38:53.926 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.926 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.927 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:38:53.927 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-05 14:38:53.927 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 14:38:53.927 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:53.946 [Information] VocomService: PTT application is not running
2025-07-05 14:38:53.948 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-05 14:38:53.951 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-05 14:38:54.755 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 14:38:54.756 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-05 14:38:54.757 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-05 14:38:54.758 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-05 14:38:54.758 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 14:38:54.759 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 14:38:54.764 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 14:38:54.765 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 14:38:54.768 [Information] BackupService: Initializing backup service
2025-07-05 14:38:54.768 [Information] BackupService: Backup service initialized successfully
2025-07-05 14:38:54.769 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 14:38:54.769 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 14:38:54.771 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 14:38:54.815 [Information] BackupService: Compressing backup data
2025-07-05 14:38:54.826 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-05 14:38:54.828 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 14:38:54.832 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 14:38:54.833 [Information] BackupService: Compressing backup data
2025-07-05 14:38:54.836 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-05 14:38:54.836 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 14:38:54.837 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 14:38:54.837 [Information] BackupService: Compressing backup data
2025-07-05 14:38:54.843 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-05 14:38:54.843 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 14:38:54.843 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 14:38:54.844 [Information] BackupService: Compressing backup data
2025-07-05 14:38:54.845 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-05 14:38:54.846 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 14:38:54.846 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 14:38:54.846 [Information] BackupService: Compressing backup data
2025-07-05 14:38:54.847 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-05 14:38:54.848 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 14:38:54.848 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 14:38:54.849 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 14:38:54.849 [Information] BackupService: Compressing backup data
2025-07-05 14:38:54.850 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-05 14:38:54.851 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 14:38:54.851 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 14:38:54.853 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 14:38:54.857 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 14:38:54.860 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 14:38:54.943 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 14:38:54.944 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 14:38:54.946 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 14:38:54.946 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 14:38:54.946 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 14:38:54.948 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 14:38:54.948 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 14:38:54.952 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 14:38:54.953 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 14:38:54.966 [Information] LicensingService: Initializing licensing service
2025-07-05 14:38:55.024 [Information] LicensingService: License information loaded successfully
2025-07-05 14:38:55.027 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 14:38:55.028 [Information] App: Licensing service initialized successfully
2025-07-05 14:38:55.028 [Information] App: License status: Trial
2025-07-05 14:38:55.028 [Information] App: Trial period: 30 days remaining
2025-07-05 14:38:55.029 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 14:38:55.209 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 14:38:55.209 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 14:38:55.209 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 14:38:55.210 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 14:38:55.210 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 14:38:55.211 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 14:38:55.211 [Information] VocomService: Native USB communication service initialized
2025-07-05 14:38:55.211 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 14:38:55.211 [Information] VocomService: Connection recovery service initialized
2025-07-05 14:38:55.212 [Information] VocomService: Enhanced services initialization completed
2025-07-05 14:38:55.212 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:38:55.225 [Information] VocomService: PTT application is not running
2025-07-05 14:38:55.227 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 14:38:55.277 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 14:38:55.277 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 14:38:55.278 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 14:38:55.278 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 14:38:55.278 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 14:38:55.280 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 14:38:55.280 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 14:38:55.281 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 14:38:55.282 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 14:38:55.282 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 14:38:55.293 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 14:38:55.294 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 14:38:55.295 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 14:38:55.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 14:38:55.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 14:38:55.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 14:38:55.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 14:38:55.297 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 14:38:55.297 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 14:38:55.299 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 14:38:55.299 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 14:38:55.300 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 14:38:55.300 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 14:38:55.300 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 14:38:55.301 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 14:38:55.301 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 14:38:55.301 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 14:38:55.304 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 14:38:55.310 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 14:38:55.312 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 14:38:55.315 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 14:38:55.318 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.326 [Information] CANRegisterAccess: Read value 0x48 from register 0x0141 (simulated)
2025-07-05 14:38:55.359 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.365 [Information] CANRegisterAccess: Read value 0x94 from register 0x0141 (simulated)
2025-07-05 14:38:55.371 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.377 [Information] CANRegisterAccess: Read value 0xFA from register 0x0141 (simulated)
2025-07-05 14:38:55.383 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.389 [Information] CANRegisterAccess: Read value 0x00 from register 0x0141 (simulated)
2025-07-05 14:38:55.394 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.400 [Information] CANRegisterAccess: Read value 0xDA from register 0x0141 (simulated)
2025-07-05 14:38:55.406 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.411 [Information] CANRegisterAccess: Read value 0x1A from register 0x0141 (simulated)
2025-07-05 14:38:55.417 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.423 [Information] CANRegisterAccess: Read value 0xDC from register 0x0141 (simulated)
2025-07-05 14:38:55.428 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.434 [Information] CANRegisterAccess: Read value 0x66 from register 0x0141 (simulated)
2025-07-05 14:38:55.440 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.446 [Information] CANRegisterAccess: Read value 0x5B from register 0x0141 (simulated)
2025-07-05 14:38:55.446 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 14:38:55.448 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 14:38:55.448 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 14:38:55.454 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 14:38:55.454 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 14:38:55.460 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 14:38:55.460 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 14:38:55.461 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 14:38:55.467 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 14:38:55.467 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 14:38:55.468 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 14:38:55.474 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 14:38:55.475 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 14:38:55.481 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 14:38:55.481 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 14:38:55.487 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 14:38:55.487 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 14:38:55.493 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 14:38:55.493 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 14:38:55.499 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 14:38:55.499 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 14:38:55.506 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 14:38:55.506 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 14:38:55.513 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 14:38:55.513 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 14:38:55.520 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 14:38:55.520 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 14:38:55.527 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 14:38:55.527 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 14:38:55.533 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 14:38:55.533 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 14:38:55.539 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 14:38:55.539 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 14:38:55.546 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 14:38:55.546 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 14:38:55.553 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 14:38:55.553 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 14:38:55.560 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 14:38:55.560 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 14:38:55.574 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 14:38:55.575 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 14:38:55.580 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 14:38:55.580 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 14:38:55.587 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 14:38:55.587 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 14:38:55.588 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 14:38:55.594 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 14:38:55.594 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 14:38:55.595 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 14:38:55.595 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.600 [Information] CANRegisterAccess: Read value 0x1B from register 0x0141 (simulated)
2025-07-05 14:38:55.606 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.611 [Information] CANRegisterAccess: Read value 0xC9 from register 0x0141 (simulated)
2025-07-05 14:38:55.617 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.623 [Information] CANRegisterAccess: Read value 0x7B from register 0x0141 (simulated)
2025-07-05 14:38:55.629 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.635 [Information] CANRegisterAccess: Read value 0x25 from register 0x0141 (simulated)
2025-07-05 14:38:55.641 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 14:38:55.647 [Information] CANRegisterAccess: Read value 0x38 from register 0x0141 (simulated)
2025-07-05 14:38:55.647 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 14:38:55.648 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 14:38:55.648 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 14:38:55.648 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 14:38:55.655 [Information] CANRegisterAccess: Read value 0x83 from register 0x0140 (simulated)
2025-07-05 14:38:55.661 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 14:38:55.667 [Information] CANRegisterAccess: Read value 0x9F from register 0x0140 (simulated)
2025-07-05 14:38:55.667 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 14:38:55.668 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 14:38:55.668 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 14:38:55.669 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 14:38:55.680 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 14:38:55.681 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 14:38:55.681 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 14:38:55.686 [Information] VocomService: Sending data and waiting for response
2025-07-05 14:38:55.686 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-05 14:38:55.738 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-05 14:38:55.740 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 14:38:55.741 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 14:38:55.742 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 14:38:55.743 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 14:38:55.754 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 14:38:55.755 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 14:38:55.755 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 14:38:55.766 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 14:38:55.777 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 14:38:55.788 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 14:38:55.799 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 14:38:55.810 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 14:38:55.810 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 14:38:55.811 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 14:38:55.822 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 14:38:55.823 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 14:38:55.823 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 14:38:55.834 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 14:38:55.845 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 14:38:55.856 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 14:38:55.867 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 14:38:55.878 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 14:38:55.889 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 14:38:55.889 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 14:38:55.890 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 14:38:55.901 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 14:38:55.902 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 14:38:55.902 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 14:38:55.903 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 14:38:55.903 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 14:38:55.903 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 14:38:55.903 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 14:38:55.904 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 14:38:55.904 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 14:38:55.904 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 14:38:55.905 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 14:38:55.905 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 14:38:55.905 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 14:38:55.905 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 14:38:55.906 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 14:38:55.906 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 14:38:55.906 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 14:38:56.006 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 14:38:56.007 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 14:38:56.007 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 14:38:56.008 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:56.008 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 14:38:56.009 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 14:38:56.009 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:56.009 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 14:38:56.010 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 14:38:56.010 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:56.010 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 14:38:56.010 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 14:38:56.011 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 14:38:56.011 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 14:38:56.011 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 14:38:56.062 [Information] BackupService: Initializing backup service
2025-07-05 14:38:56.063 [Information] BackupService: Backup service initialized successfully
2025-07-05 14:38:56.113 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 14:38:56.114 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 14:38:56.115 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 14:38:56.116 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 14:38:56.168 [Information] BackupService: Getting predefined backup categories
2025-07-05 14:38:56.220 [Information] MainViewModel: Services initialized successfully
2025-07-05 14:38:56.223 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 14:38:56.224 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 14:38:56.225 [Information] VocomService: Using new enhanced device detection service
2025-07-05 14:38:56.225 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 14:38:56.225 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 14:38:56.440 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 14:38:56.441 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 14:38:56.441 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 14:38:56.441 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 14:38:56.442 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 14:38:56.442 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 14:38:56.442 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 14:38:56.443 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 14:38:56.782 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 14:38:56.783 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 14:38:56.783 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 14:38:56.784 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 14:38:56.784 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 14:38:56.784 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 14:38:56.785 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 14:38:56.826 [Information] VocomService: Found 3 Vocom devices
2025-07-05 14:38:56.828 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-05 14:39:16.623 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-05 14:39:16.623 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:16.626 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-05 14:39:16.627 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-05 14:39:17.028 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-05 14:39:17.029 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-05 14:39:17.030 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 14:39:17.034 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 14:39:17.035 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 14:39:17.035 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-05 14:39:17.036 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-05 14:39:17.036 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 14:39:17.036 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 14:39:17.037 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:39:17.052 [Information] VocomService: PTT application is not running
2025-07-05 14:39:17.053 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-05 14:39:17.053 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-05 14:39:17.053 [Information] VocomService: Checking if PTT application is running
2025-07-05 14:39:17.066 [Information] VocomService: PTT application is not running
2025-07-05 14:39:17.067 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-05 14:39:17.067 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-05 14:39:17.067 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-05 14:39:17.068 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 14:39:17.068 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-05 14:39:17.068 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 14:39:17.068 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-05 14:39:17.069 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 14:39:17.069 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 14:39:17.069 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 14:39:17.069 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 14:39:17.070 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-05 14:39:17.070 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:39:17.070 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:39:17.071 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:39:17.071 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:39:17.071 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:39:17.072 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-05 14:39:17.072 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:17.072 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:17.072 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:17.073 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:17.073 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:17.073 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-05 14:39:17.074 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
2025-07-05 05:01:53.340 [Information] LoggingService: Logging service initialized
2025-07-05 05:01:53.351 [Information] App: Starting integrated application initialization
2025-07-05 05:01:53.352 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 05:01:53.354 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 05:01:53.356 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 05:01:53.356 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 05:01:53.357 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 05:01:53.358 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 05:01:53.360 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 05:01:53.486 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 05:01:53.592 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 05:01:53.622 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 05:01:53.640 [Information] VCRedistBundler: Copied api-ms-win-crt-runtime-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:01:53.667 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 05:01:53.684 [Information] VCRedistBundler: Copied api-ms-win-crt-heap-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 05:01:53.709 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-05 05:01:53.726 [Information] VCRedistBundler: Copied api-ms-win-crt-string-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-string-l1-1-0.dll
2025-07-05 05:01:53.727 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 05:01:53.729 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 05:01:53.729 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 05:01:53.735 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 05:01:53.735 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 05:01:53.739 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 05:01:53.739 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 05:01:53.740 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 05:01:53.750 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 05:01:53.750 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 05:01:53.756 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 05:01:53.756 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:01:53.761 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:01:53.761 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 05:01:53.766 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 05:01:53.766 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 05:01:53.770 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 05:01:53.772 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-05 05:01:53.773 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-05 05:01:53.775 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-05 05:01:53.775 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-05 05:01:53.776 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 05:01:53.777 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 05:01:53.779 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 05:01:53.780 [Information] LibraryExtractor: Copying system libraries
2025-07-05 05:01:53.788 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 05:01:53.809 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 05:02:02.545 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 05:02:16.046 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 05:02:38.173 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 05:02:38.173 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 05:02:38.173 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 05:02:38.174 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 05:02:38.174 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 05:02:38.174 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 05:02:38.177 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 05:02:38.178 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 05:02:38.179 [Information] DependencyManager: Initializing dependency manager
2025-07-05 05:02:38.180 [Information] DependencyManager: Setting up library search paths
2025-07-05 05:02:38.180 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 05:02:38.181 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 05:02:38.181 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 05:02:38.181 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 05:02:38.182 [Information] DependencyManager: Verifying required directories
2025-07-05 05:02:38.183 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 05:02:38.183 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 05:02:38.183 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 05:02:38.183 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 05:02:38.185 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 05:02:38.189 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-05 05:02:38.189 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-05 05:02:38.191 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-05 05:02:38.192 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-05 05:02:38.192 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-05 05:02:38.193 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-05 05:02:38.194 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 05:02:38.198 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 05:02:38.199 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 05:02:38.199 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:02:38.200 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 05:02:38.201 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 05:02:38.202 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 05:02:38.206 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 05:02:38.264 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 05:02:38.265 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 05:02:38.326 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 05:02:38.327 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 05:02:38.328 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 05:02:38.371 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 05:02:38.372 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 05:02:38.417 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 05:02:38.417 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 05:02:38.418 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 05:02:38.589 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 05:02:38.590 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 05:02:38.763 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 05:02:38.766 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 05:02:38.767 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 05:02:38.837 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 05:02:38.837 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 05:02:38.909 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 05:02:38.909 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 05:02:38.910 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 05:02:38.955 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-05 05:02:38.955 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 05:02:38.956 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 05:02:38.957 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-05 05:02:38.957 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-05 05:02:38.958 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-05 05:02:38.958 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-05 05:02:38.958 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-05 05:02:38.959 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-05 05:02:38.959 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 05:02:38.960 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 05:02:38.960 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 05:02:38.960 [Information] DependencyManager: Setting up environment variables
2025-07-05 05:02:38.961 [Information] DependencyManager: Environment variables configured
2025-07-05 05:02:38.962 [Information] DependencyManager: Verifying library loading status
2025-07-05 05:02:39.063 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-05 05:02:39.063 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 05:02:39.064 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 05:02:39.065 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 05:02:39.066 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 05:02:39.074 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 05:02:39.075 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 05:02:39.075 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 05:02:39.076 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 05:02:39.076 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 05:02:39.077 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 05:02:39.077 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 05:02:39.077 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 05:02:39.077 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 05:02:39.077 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 05:02:39.078 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 05:02:39.078 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 05:02:39.078 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 05:02:39.078 [Information] App: Integrated startup completed successfully
2025-07-05 05:02:39.080 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 05:02:39.194 [Information] App: Initializing application services
2025-07-05 05:02:39.195 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 05:02:39.196 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 05:02:39.240 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 05:02:39.241 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 05:02:39.242 [Information] App: Configuration service initialized successfully
2025-07-05 05:02:39.242 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 05:02:39.243 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 05:02:39.246 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 05:02:39.246 [Information] App: Final useDummyImplementations value: False
2025-07-05 05:02:39.246 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 05:02:39.247 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-05 05:02:39.258 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 05:02:39.259 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 05:02:39.259 [Information] App: usePatchedImplementation flag is: True
2025-07-05 05:02:39.260 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 05:02:39.260 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 05:02:39.260 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 05:02:39.261 [Information] App: verboseLogging flag is: True
2025-07-05 05:02:39.262 [Information] App: Verifying real hardware requirements...
2025-07-05 05:02:39.263 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 05:02:39.263 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 05:02:39.263 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 05:02:39.264 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 05:02:39.264 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 05:02:39.265 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 05:02:39.265 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 05:02:39.265 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 05:02:39.276 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-05 05:02:39.276 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-05 05:02:39.277 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-05 05:02:39.277 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-05 05:02:39.278 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-05 05:02:39.292 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-05 05:02:39.293 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-05 05:02:39.293 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-05 05:02:39.293 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-05 05:02:39.294 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 05:02:39.315 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-05 05:02:39.318 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-05 05:02:39.318 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-05 05:02:39.318 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-05 05:02:39.319 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-05 05:02:39.320 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-05 05:02:39.320 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-05 05:02:39.320 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-05 05:02:39.321 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:02:39.321 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-05 05:02:39.321 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-05 05:02:39.321 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-05 05:02:39.323 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-05 05:02:39.324 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-05 05:02:39.331 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-05 05:02:39.332 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 05:02:39.332 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-05 05:02:39.338 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll
2025-07-05 05:02:39.340 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 05:02:39.342 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apcidb.dll
2025-07-05 05:02:39.343 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 05:02:39.348 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-05 05:02:39.351 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 05:02:39.353 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-05 05:02:39.355 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 05:02:39.356 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-05 05:02:39.357 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusTea2Data.dll
2025-07-05 05:02:39.359 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 05:02:39.360 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-07-05 05:02:39.362 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 05:02:39.363 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-05 05:02:39.364 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-05 05:02:39.365 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Core.dll
2025-07-05 05:02:39.367 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-05 05:02:39.368 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Logging.dll
2025-07-05 05:02:39.369 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-05 05:02:39.370 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.dll
2025-07-05 05:02:39.371 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 05:02:39.372 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-07-05 05:02:39.373 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-05 05:02:39.374 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Baf.Utility.dll
2025-07-05 05:02:39.375 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 05:02:39.376 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-05 05:02:39.378 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-05 05:02:39.379 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.ServiceContract.dll
2025-07-05 05:02:39.380 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-05 05:02:39.381 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.Utility.dll
2025-07-05 05:02:39.383 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-05 05:02:39.384 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll.config
2025-07-05 05:02:39.385 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-05 05:02:39.386 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll.config
2025-07-05 05:02:39.391 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-05 05:02:39.394 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.dll
2025-07-05 05:02:39.395 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-05 05:02:39.396 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.Caches.SysCache2.dll
2025-07-05 05:02:39.397 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-05 05:02:39.398 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Iesi.Collections.dll
2025-07-05 05:02:39.400 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-05 05:02:39.401 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Ionic.Zip.Reduced.dll
2025-07-05 05:02:39.403 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-05 05:02:39.405 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-05 05:02:39.406 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\DotNetZip.dll
2025-07-05 05:02:39.408 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-05 05:02:39.410 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\ICSharpCode.SharpZipLib.dll
2025-07-05 05:02:39.411 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-05 05:02:39.412 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.CommonDomain.Model.dll
2025-07-05 05:02:39.414 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-05 05:02:39.415 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.Contracts.Common.dll
2025-07-05 05:02:39.417 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-05 05:02:39.418 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.UtilityComponent.dll
2025-07-05 05:02:39.419 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-05 05:02:39.420 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\log4net.dll
2025-07-05 05:02:39.422 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-05 05:02:39.424 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-05 05:02:39.425 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\AutoMapper.dll
2025-07-05 05:02:39.426 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-05 05:02:39.428 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-07-05 05:02:39.429 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.AppContext.dll
2025-07-05 05:02:39.430 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-07-05 05:02:39.431 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Buffers.dll
2025-07-05 05:02:39.433 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-05 05:02:39.433 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Concurrent.dll
2025-07-05 05:02:39.435 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.dll
2025-07-05 05:02:39.436 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.dll
2025-07-05 05:02:39.437 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-05 05:02:39.438 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.NonGeneric.dll
2025-07-05 05:02:39.439 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-05 05:02:39.440 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Specialized.dll
2025-07-05 05:02:39.441 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-05 05:02:39.442 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.dll
2025-07-05 05:02:39.443 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-05 05:02:39.444 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.EventBasedAsync.dll
2025-07-05 05:02:39.445 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-05 05:02:39.446 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.Primitives.dll
2025-07-05 05:02:39.447 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-05 05:02:39.447 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.TypeConverter.dll
2025-07-05 05:02:39.448 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Console.dll
2025-07-05 05:02:39.449 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Console.dll
2025-07-05 05:02:39.451 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-05 05:02:39.451 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.Common.dll
2025-07-05 05:02:39.453 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-05 05:02:39.454 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SQLite.dll
2025-07-05 05:02:39.456 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-05 05:02:39.457 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SqlServerCe.dll
2025-07-05 05:02:39.458 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-05 05:02:39.459 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Contracts.dll
2025-07-05 05:02:39.461 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-05 05:02:39.461 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Debug.dll
2025-07-05 05:02:39.463 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-05 05:02:39.463 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.FileVersionInfo.dll
2025-07-05 05:02:39.466 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-05 05:02:39.467 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Process.dll
2025-07-05 05:02:39.469 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-05 05:02:39.470 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.StackTrace.dll
2025-07-05 05:02:39.471 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 05:02:39.472 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-07-05 05:02:39.473 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-05 05:02:39.474 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tools.dll
2025-07-05 05:02:39.475 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-05 05:02:39.476 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TraceSource.dll
2025-07-05 05:02:39.477 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-05 05:02:39.478 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tracing.dll
2025-07-05 05:02:39.479 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-05 05:02:39.480 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Drawing.Primitives.dll
2025-07-05 05:02:39.481 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-05 05:02:39.482 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Dynamic.Runtime.dll
2025-07-05 05:02:39.483 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-05 05:02:39.484 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Calendars.dll
2025-07-05 05:02:39.486 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-07-05 05:02:39.487 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.dll
2025-07-05 05:02:39.488 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-05 05:02:39.489 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Extensions.dll
2025-07-05 05:02:39.491 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-05 05:02:39.492 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.dll
2025-07-05 05:02:39.493 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-05 05:02:39.493 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.ZipFile.dll
2025-07-05 05:02:39.495 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.dll
2025-07-05 05:02:39.495 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.dll
2025-07-05 05:02:39.496 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-05 05:02:39.497 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.dll
2025-07-05 05:02:39.498 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-05 05:02:39.499 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.DriveInfo.dll
2025-07-05 05:02:39.500 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-05 05:02:39.501 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Primitives.dll
2025-07-05 05:02:39.502 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-05 05:02:39.503 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Watcher.dll
2025-07-05 05:02:39.505 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-05 05:02:39.505 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.IsolatedStorage.dll
2025-07-05 05:02:39.507 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-05 05:02:39.508 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.MemoryMappedFiles.dll
2025-07-05 05:02:39.509 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-05 05:02:39.510 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Pipes.dll
2025-07-05 05:02:39.511 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-05 05:02:39.512 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.UnmanagedMemoryStream.dll
2025-07-05 05:02:39.513 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.dll
2025-07-05 05:02:39.513 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.dll
2025-07-05 05:02:39.515 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-05 05:02:39.515 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Expressions.dll
2025-07-05 05:02:39.517 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-05 05:02:39.518 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Parallel.dll
2025-07-05 05:02:39.520 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-05 05:02:39.521 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Queryable.dll
2025-07-05 05:02:39.523 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Memory.dll
2025-07-05 05:02:39.524 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Memory.dll
2025-07-05 05:02:39.526 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-05 05:02:39.527 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Http.dll
2025-07-05 05:02:39.528 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-05 05:02:39.529 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NameResolution.dll
2025-07-05 05:02:39.530 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-05 05:02:39.531 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NetworkInformation.dll
2025-07-05 05:02:39.532 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-05 05:02:39.533 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Ping.dll
2025-07-05 05:02:39.534 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-05 05:02:39.535 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Primitives.dll
2025-07-05 05:02:39.537 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-05 05:02:39.537 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Requests.dll
2025-07-05 05:02:39.539 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-05 05:02:39.539 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Security.dll
2025-07-05 05:02:39.541 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-05 05:02:39.542 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Sockets.dll
2025-07-05 05:02:39.543 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-05 05:02:39.544 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebHeaderCollection.dll
2025-07-05 05:02:39.545 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-05 05:02:39.546 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.Client.dll
2025-07-05 05:02:39.547 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-05 05:02:39.548 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.dll
2025-07-05 05:02:39.550 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-05 05:02:39.551 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Numerics.Vectors.dll
2025-07-05 05:02:39.552 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-05 05:02:39.553 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ObjectModel.dll
2025-07-05 05:02:39.554 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-07-05 05:02:39.555 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.dll
2025-07-05 05:02:39.557 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-05 05:02:39.558 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Extensions.dll
2025-07-05 05:02:39.559 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-05 05:02:39.560 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Primitives.dll
2025-07-05 05:02:39.561 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-05 05:02:39.562 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Reader.dll
2025-07-05 05:02:39.563 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-05 05:02:39.564 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.ResourceManager.dll
2025-07-05 05:02:39.565 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-05 05:02:39.565 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Writer.dll
2025-07-05 05:02:39.567 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 05:02:39.568 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-07-05 05:02:39.570 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 05:02:39.571 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.VisualC.dll
2025-07-05 05:02:39.572 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-07-05 05:02:39.573 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.dll
2025-07-05 05:02:39.574 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-05 05:02:39.575 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Extensions.dll
2025-07-05 05:02:39.576 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-05 05:02:39.577 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Handles.dll
2025-07-05 05:02:39.578 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-05 05:02:39.578 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.dll
2025-07-05 05:02:39.579 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 05:02:39.580 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-05 05:02:39.581 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-05 05:02:39.582 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Numerics.dll
2025-07-05 05:02:39.583 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-05 05:02:39.584 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Formatters.dll
2025-07-05 05:02:39.585 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-05 05:02:39.586 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Json.dll
2025-07-05 05:02:39.587 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-05 05:02:39.587 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Primitives.dll
2025-07-05 05:02:39.588 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-05 05:02:39.589 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Xml.dll
2025-07-05 05:02:39.590 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-05 05:02:39.591 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Claims.dll
2025-07-05 05:02:39.592 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-05 05:02:39.592 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Algorithms.dll
2025-07-05 05:02:39.593 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-05 05:02:39.594 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Csp.dll
2025-07-05 05:02:39.595 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-05 05:02:39.596 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Encoding.dll
2025-07-05 05:02:39.597 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-05 05:02:39.597 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Primitives.dll
2025-07-05 05:02:39.598 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-05 05:02:39.599 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.X509Certificates.dll
2025-07-05 05:02:39.600 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-05 05:02:39.601 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Principal.dll
2025-07-05 05:02:39.602 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-05 05:02:39.604 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.SecureString.dll
2025-07-05 05:02:39.606 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-05 05:02:39.609 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.dll
2025-07-05 05:02:39.610 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-05 05:02:39.611 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.Extensions.dll
2025-07-05 05:02:39.612 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-05 05:02:39.613 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.RegularExpressions.dll
2025-07-05 05:02:39.614 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.dll
2025-07-05 05:02:39.615 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.dll
2025-07-05 05:02:39.617 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-05 05:02:39.618 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Overlapped.dll
2025-07-05 05:02:39.619 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-05 05:02:39.620 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.dll
2025-07-05 05:02:39.621 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-05 05:02:39.622 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Extensions.dll
2025-07-05 05:02:39.623 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-05 05:02:39.624 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Parallel.dll
2025-07-05 05:02:39.625 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-05 05:02:39.626 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Thread.dll
2025-07-05 05:02:39.627 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-05 05:02:39.627 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.ThreadPool.dll
2025-07-05 05:02:39.628 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-05 05:02:39.629 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Timer.dll
2025-07-05 05:02:39.630 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-05 05:02:39.631 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.ValueTuple.dll
2025-07-05 05:02:39.632 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-05 05:02:39.633 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.ReaderWriter.dll
2025-07-05 05:02:39.634 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-05 05:02:39.635 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XDocument.dll
2025-07-05 05:02:39.637 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-05 05:02:39.638 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlDocument.dll
2025-07-05 05:02:39.639 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-05 05:02:39.641 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlSerializer.dll
2025-07-05 05:02:39.643 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-05 05:02:39.644 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.dll
2025-07-05 05:02:39.645 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-05 05:02:39.645 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.XDocument.dll
2025-07-05 05:02:39.647 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-07-05 05:02:39.648 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\SystemInterface.dll
2025-07-05 05:02:39.648 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-05 05:02:39.649 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-05 05:02:39.649 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-05 05:02:39.713 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 05:02:39.713 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-05 05:02:39.713 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 05:02:39.714 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-05 05:02:39.779 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 05:02:39.779 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-05 05:02:39.780 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-05 05:02:39.780 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-05 05:02:39.781 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-05 05:02:39.781 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-05 05:02:39.782 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-05 05:02:39.782 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-05 05:02:39.784 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-05 05:02:39.785 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-05 05:02:39.785 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 05:02:39.785 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-05 05:02:39.785 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-05 05:02:39.786 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-05 05:02:39.787 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-05 05:02:39.787 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-05 05:02:39.787 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-05 05:02:39.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-05 05:02:39.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-05 05:02:39.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-05 05:02:39.788 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-05 05:02:39.789 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-07-05 05:02:39.790 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-07-05 05:02:39.790 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-05 05:02:39.791 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-05 05:02:39.791 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-05 05:02:39.791 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:02:39.791 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-05 05:02:39.792 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-05 05:02:39.792 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 05:02:39.793 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-05 05:02:39.793 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-05 05:02:39.793 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-05 05:02:39.794 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-05 05:02:39.794 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-05 05:02:39.795 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-05 05:02:39.796 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-05 05:02:39.796 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-05 05:02:39.806 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-05 05:02:39.807 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-05 05:02:39.807 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 05:02:39.807 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 05:02:39.808 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-05 05:02:39.809 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:39.810 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:39.810 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-05 05:02:39.859 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:39.860 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:39.860 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-05 05:02:40.082 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:40.083 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-05 05:02:40.160 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:40.161 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-05 05:02:40.249 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:40.250 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:40.250 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-05 05:02:40.330 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:40.413 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:40.413 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-05 05:02:40.480 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 05:02:40.583 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 05:02:40.673 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:40.758 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:40.758 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-05 05:02:40.759 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 05:02:40.930 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:40.930 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-05 05:02:40.995 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:41.062 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:41.062 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-05 05:02:41.170 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-05 05:02:41.282 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-05 05:02:41.282 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-05 05:02:41.382 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 05:02:41.389 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 05:02:41.431 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-05 05:02:41.433 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-05 05:02:41.433 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 05:02:41.434 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-05 05:02:41.434 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-05 05:02:41.434 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-05 05:02:41.435 [Information] VocomDriver: Initializing Vocom driver
2025-07-05 05:02:41.436 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-05 05:02:41.438 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-05 05:02:41.438 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 05:02:41.438 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 05:02:41.439 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 05:02:41.439 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-05 05:02:41.440 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-05 05:02:41.441 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-05 05:02:41.442 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-05 05:02:41.442 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-05 05:02:41.443 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-05 05:02:41.443 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 05:02:41.446 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-05 05:02:41.448 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-05 05:02:41.451 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-05 05:02:41.451 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-05 05:02:41.451 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-05 05:02:41.452 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-05 05:02:41.452 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-05 05:02:41.453 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-05 05:02:41.453 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-05 05:02:41.453 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-05 05:02:41.453 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-05 05:02:41.453 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-05 05:02:41.454 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-05 05:02:41.454 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-05 05:02:41.454 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-05 05:02:41.454 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-05 05:02:41.454 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-05 05:02:41.455 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-05 05:02:41.455 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-05 05:02:41.455 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-05 05:02:41.455 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-05 05:02:41.455 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-05 05:02:41.455 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-05 05:02:41.456 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-05 05:02:41.456 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-05 05:02:41.456 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-05 05:02:41.456 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-05 05:02:41.456 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-05 05:02:41.457 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-05 05:02:41.457 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-05 05:02:41.457 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-05 05:02:41.457 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-05 05:02:41.457 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-05 05:02:41.458 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-05 05:02:41.458 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-05 05:02:41.458 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-05 05:02:41.459 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-05 05:02:41.459 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-05 05:02:41.459 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-05 05:02:41.459 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-05 05:02:41.459 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-05 05:02:41.460 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-05 05:02:41.460 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-05 05:02:41.460 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-05 05:02:41.460 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-05 05:02:41.460 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-05 05:02:41.461 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-05 05:02:41.461 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-05 05:02:41.461 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-05 05:02:41.462 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-05 05:02:41.462 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-05 05:02:41.463 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-05 05:02:41.463 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-05 05:02:41.463 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-05 05:02:41.463 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-05 05:02:41.464 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-05 05:02:41.464 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-05 05:02:41.466 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-05 05:02:41.467 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-05 05:02:41.467 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-05 05:02:41.468 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-05 05:02:41.505 [Information] WiFiCommunicationService: WiFi is available
2025-07-05 05:02:41.506 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-05 05:02:41.507 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-05 05:02:41.507 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-05 05:02:41.508 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-05 05:02:41.509 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-05 05:02:41.510 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 05:02:41.510 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 05:02:41.511 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 05:02:41.512 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 05:02:41.515 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 05:02:41.515 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 05:02:41.515 [Information] VocomService: Native USB communication service initialized
2025-07-05 05:02:41.515 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 05:02:41.516 [Information] VocomService: Connection recovery service initialized
2025-07-05 05:02:41.516 [Information] VocomService: Enhanced services initialization completed
2025-07-05 05:02:41.518 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:41.525 [Information] VocomService: PTT application is not running
2025-07-05 05:02:41.526 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 05:02:41.527 [Debug] VocomService: Bluetooth is enabled
2025-07-05 05:02:41.528 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 05:02:41.528 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-05 05:02:41.528 [Information] App: Initializing Vocom service
2025-07-05 05:02:41.528 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 05:02:41.528 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 05:02:41.529 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 05:02:41.529 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 05:02:41.529 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 05:02:41.529 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 05:02:41.530 [Information] VocomService: Native USB communication service initialized
2025-07-05 05:02:41.530 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 05:02:41.530 [Information] VocomService: Connection recovery service initialized
2025-07-05 05:02:41.530 [Information] VocomService: Enhanced services initialization completed
2025-07-05 05:02:41.531 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:41.539 [Information] VocomService: PTT application is not running
2025-07-05 05:02:41.539 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 05:02:41.540 [Debug] VocomService: Bluetooth is enabled
2025-07-05 05:02:41.540 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 05:02:41.542 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:02:41.542 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:02:41.543 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:02:41.544 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:02:41.802 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 05:02:41.802 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 05:02:41.803 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:02:41.804 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:02:41.805 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 05:02:41.906 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 05:02:41.907 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 05:02:41.908 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 05:02:41.908 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 05:02:41.910 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-05 05:02:41.910 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:41.920 [Information] VocomService: PTT application is not running
2025-07-05 05:02:41.922 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-05 05:02:41.923 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:41.923 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:41.930 [Information] VocomService: PTT application is not running
2025-07-05 05:02:41.931 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:41.932 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:41.933 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-05 05:02:41.934 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 05:02:41.934 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:41.961 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 05:02:41.962 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:41.963 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 05:02:41.964 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 05:02:41.965 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 05:02:41.965 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 05:02:41.965 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-05 05:02:41.965 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-05 05:02:41.966 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:41.966 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-05 05:02:41.968 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 05:02:41.971 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:41.971 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 05:02:41.974 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 05:02:41.976 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 05:02:41.976 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 05:02:41.978 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 05:02:41.979 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 05:02:41.991 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 05:02:41.992 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 05:02:42.004 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 05:02:42.008 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 05:02:42.010 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 05:02:42.010 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:42.012 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 05:02:42.012 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 05:02:42.012 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:42.013 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 05:02:42.014 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 05:02:42.014 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:42.015 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 05:02:42.015 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 05:02:42.015 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:42.017 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 05:02:42.017 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 05:02:42.017 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:42.018 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 05:02:42.020 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 05:02:42.021 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:42.021 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 05:02:42.021 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 05:02:42.021 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:42.022 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 05:02:42.022 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 05:02:42.022 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:42.022 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 05:02:42.023 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 05:02:42.023 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:42.023 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 05:02:42.023 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 05:02:42.024 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 05:02:42.025 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-05 05:02:42.025 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:02:42.026 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:02:42.026 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:02:42.026 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:02:42.198 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 05:02:42.198 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 05:02:42.198 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:02:42.198 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:02:42.199 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 05:02:42.299 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 05:02:42.299 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 05:02:42.300 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 05:02:42.300 [Information] VocomService: Attempting to connect to Vocom device 88890300 via USB
2025-07-05 05:02:42.300 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-05 05:02:42.301 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:42.308 [Information] VocomService: PTT application is not running
2025-07-05 05:02:42.308 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-05 05:02:42.309 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:42.309 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:42.317 [Information] VocomService: PTT application is not running
2025-07-05 05:02:42.318 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:42.318 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:42.318 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-05 05:02:42.318 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 05:02:42.319 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:42.319 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 05:02:42.319 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:42.319 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 05:02:42.319 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 05:02:42.320 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 05:02:42.320 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 05:02:42.320 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-05 05:02:42.320 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-05 05:02:42.321 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:42.321 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:42.321 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:42.322 [Warning] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:42.322 [Error] VocomService: Failed to connect to any Vocom device
2025-07-05 05:02:42.322 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-05 05:02:42.323 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-05 05:02:42.324 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-05 05:02:43.325 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-05 05:02:43.325 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 05:02:43.325 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 05:02:43.326 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 05:02:43.326 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 05:02:43.326 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 05:02:43.327 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 05:02:43.327 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 05:02:43.328 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 05:02:43.328 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 05:02:43.329 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 05:02:43.329 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:43.329 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 05:02:43.329 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 05:02:43.330 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:43.330 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 05:02:43.330 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 05:02:43.330 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:43.331 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 05:02:43.331 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 05:02:43.331 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:43.331 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 05:02:43.331 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 05:02:43.332 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:43.332 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 05:02:43.332 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 05:02:43.332 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:43.332 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 05:02:43.333 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 05:02:43.333 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:43.333 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 05:02:43.333 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 05:02:43.333 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:43.334 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 05:02:43.334 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 05:02:43.334 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:43.334 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 05:02:43.334 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 05:02:43.334 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 05:02:43.335 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-05 05:02:43.335 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:02:43.335 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:02:43.335 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:02:43.335 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:02:43.498 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 05:02:43.498 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 05:02:43.499 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:02:43.499 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:02:43.499 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 05:02:43.600 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 05:02:43.600 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 05:02:43.600 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 05:02:43.600 [Information] VocomService: Attempting to connect to Vocom device 88890300 via USB
2025-07-05 05:02:43.601 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-05 05:02:43.601 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:43.608 [Information] VocomService: PTT application is not running
2025-07-05 05:02:43.608 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-05 05:02:43.608 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:43.608 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:43.615 [Information] VocomService: PTT application is not running
2025-07-05 05:02:43.616 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:43.616 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:43.616 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-05 05:02:43.616 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 05:02:43.617 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:43.617 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 05:02:43.617 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:43.618 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 05:02:43.618 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 05:02:43.618 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 05:02:43.618 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 05:02:43.618 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-05 05:02:43.619 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-05 05:02:43.619 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:43.619 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:43.619 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:43.620 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:43.620 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:43.620 [Warning] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:43.620 [Error] VocomService: Failed to connect to any Vocom device
2025-07-05 05:02:43.620 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-05 05:02:43.621 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-05 05:02:43.621 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-05 05:02:43.621 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-05 05:02:45.622 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-05 05:02:45.622 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 05:02:45.623 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 05:02:45.623 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 05:02:45.623 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 05:02:45.623 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 05:02:45.624 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 05:02:45.624 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 05:02:45.625 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 05:02:45.625 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 05:02:45.626 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 05:02:45.626 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:45.626 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 05:02:45.626 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 05:02:45.627 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:45.627 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 05:02:45.627 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 05:02:45.627 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:45.627 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 05:02:45.628 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 05:02:45.628 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:45.628 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 05:02:45.628 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 05:02:45.628 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 05:02:45.629 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 05:02:45.629 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 05:02:45.629 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:45.629 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 05:02:45.630 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 05:02:45.630 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:45.630 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 05:02:45.630 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 05:02:45.631 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:45.631 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 05:02:45.631 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 05:02:45.631 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 05:02:45.631 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 05:02:45.632 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 05:02:45.632 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 05:02:45.632 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-05 05:02:45.632 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:02:45.632 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:02:45.633 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:02:45.633 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:02:45.789 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 05:02:45.790 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 05:02:45.790 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:02:45.790 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:02:45.790 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 05:02:45.891 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 05:02:45.891 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 05:02:45.891 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 05:02:45.892 [Information] VocomService: Attempting to connect to Vocom device 88890300 via USB
2025-07-05 05:02:45.892 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-05 05:02:45.892 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:45.899 [Information] VocomService: PTT application is not running
2025-07-05 05:02:45.899 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-05 05:02:45.900 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:45.900 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:45.907 [Information] VocomService: PTT application is not running
2025-07-05 05:02:45.908 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:45.908 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:45.908 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-05 05:02:45.909 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 05:02:45.909 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:45.909 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 05:02:45.910 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:45.910 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 05:02:45.910 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 05:02:45.910 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 05:02:45.911 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 05:02:45.911 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-05 05:02:45.911 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-05 05:02:45.911 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:45.912 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:45.912 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:45.912 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:45.913 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:45.913 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:45.913 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:45.913 [Warning] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:45.914 [Error] VocomService: Failed to connect to any Vocom device
2025-07-05 05:02:45.914 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-05 05:02:45.914 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-05 05:02:45.915 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-05 05:02:45.915 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-05 05:02:45.915 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-05 05:02:48.916 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-05 05:02:48.916 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-05 05:02:48.917 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-05 05:02:48.918 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-05 05:02:49.419 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-05 05:02:49.419 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-05 05:02:49.420 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-05 05:02:49.420 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-05 05:02:49.422 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 05:02:49.423 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 05:02:49.425 [Information] BackupService: Initializing backup service
2025-07-05 05:02:49.425 [Information] BackupService: Backup service initialized successfully
2025-07-05 05:02:49.425 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 05:02:49.425 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 05:02:49.427 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 05:02:49.448 [Information] BackupService: Compressing backup data
2025-07-05 05:02:49.458 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-05 05:02:49.459 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 05:02:49.459 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 05:02:49.459 [Information] BackupService: Compressing backup data
2025-07-05 05:02:49.461 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-05 05:02:49.461 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 05:02:49.461 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 05:02:49.461 [Information] BackupService: Compressing backup data
2025-07-05 05:02:49.462 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-05 05:02:49.462 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 05:02:49.463 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 05:02:49.463 [Information] BackupService: Compressing backup data
2025-07-05 05:02:49.464 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-05 05:02:49.464 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 05:02:49.464 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 05:02:49.465 [Information] BackupService: Compressing backup data
2025-07-05 05:02:49.466 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-07-05 05:02:49.466 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 05:02:49.466 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 05:02:49.467 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 05:02:49.467 [Information] BackupService: Compressing backup data
2025-07-05 05:02:49.468 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (511 bytes)
2025-07-05 05:02:49.468 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 05:02:49.469 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 05:02:49.470 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 05:02:49.472 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 05:02:49.474 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 05:02:49.519 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 05:02:49.519 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 05:02:49.520 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 05:02:49.520 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 05:02:49.521 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 05:02:49.521 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 05:02:49.522 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 05:02:49.524 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 05:02:49.524 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 05:02:49.531 [Information] LicensingService: Initializing licensing service
2025-07-05 05:02:49.598 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-05 05:02:49.600 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 05:02:49.600 [Information] App: Licensing service initialized successfully
2025-07-05 05:02:49.600 [Information] App: License status: Trial
2025-07-05 05:02:49.601 [Information] App: Trial period: 30 days remaining
2025-07-05 05:02:49.601 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 05:02:49.748 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-05 05:02:49.748 [Information] VocomService: Initializing enhanced Vocom services
2025-07-05 05:02:49.748 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-05 05:02:49.749 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-05 05:02:49.749 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-05 05:02:49.749 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-05 05:02:49.750 [Information] VocomService: Native USB communication service initialized
2025-07-05 05:02:49.750 [Information] VocomService: Enhanced device detection service initialized
2025-07-05 05:02:49.750 [Information] VocomService: Connection recovery service initialized
2025-07-05 05:02:49.750 [Information] VocomService: Enhanced services initialization completed
2025-07-05 05:02:49.751 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:49.757 [Information] VocomService: PTT application is not running
2025-07-05 05:02:49.757 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 05:02:49.758 [Debug] VocomService: Bluetooth is enabled
2025-07-05 05:02:49.758 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-05 05:02:49.809 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-05 05:02:50.309 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-05 05:02:50.360 [Information] BackupService: Initializing backup service
2025-07-05 05:02:50.360 [Information] BackupService: Backup service initialized successfully
2025-07-05 05:02:50.411 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 05:02:50.411 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 05:02:50.412 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build (4)\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 05:02:50.412 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 05:02:50.463 [Information] BackupService: Getting predefined backup categories
2025-07-05 05:02:50.514 [Information] MainViewModel: Services initialized successfully
2025-07-05 05:02:50.516 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 05:02:50.517 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:02:50.517 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:02:50.517 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:02:50.518 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:02:50.673 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 05:02:50.674 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 05:02:50.674 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:02:50.674 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:02:50.674 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 05:02:50.780 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 05:02:50.780 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 05:02:50.781 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 05:02:50.782 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-05 05:02:56.090 [Information] MainViewModel: Connecting to Vocom device 88890300
2025-07-05 05:02:56.091 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-05 05:02:56.091 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:56.098 [Information] VocomService: PTT application is not running
2025-07-05 05:02:56.099 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-05 05:02:56.099 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:56.099 [Information] VocomService: Checking if PTT application is running
2025-07-05 05:02:56.106 [Information] VocomService: PTT application is not running
2025-07-05 05:02:56.106 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:56.106 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:56.107 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-05 05:02:56.107 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-05 05:02:56.107 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:56.107 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-05 05:02:56.107 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-05 05:02:56.108 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-05 05:02:56.108 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-05 05:02:56.108 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-05 05:02:56.108 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-05 05:02:56.109 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-05 05:02:56.109 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-05 05:02:56.109 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:56.109 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:56.109 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:56.110 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300
2025-07-05 05:02:56.110 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:56.110 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:56.110 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:56.110 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:56.111 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-05 05:02:56.111 [Error] MainViewModel: Failed to connect to Vocom device 88890300
2025-07-05 05:03:01.007 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 05:03:01.008 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:03:01.009 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:03:01.009 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:03:01.009 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:03:01.175 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-05 05:03:01.175 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:03:01.175 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:03:01.175 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-05 05:03:01.176 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-05 05:03:01.176 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-05 05:03:01.178 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-05 05:03:01.179 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-05 05:03:01.347 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-05 05:03:01.349 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-05 05:03:01.350 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-05 05:03:01.350 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-05 05:03:01.350 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-05 05:03:01.350 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-05 05:03:01.351 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-05 05:03:01.351 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-05 05:03:01.352 [Debug] VocomService: Bluetooth is enabled
2025-07-05 05:03:01.353 [Debug] VocomService: Checking if WiFi is available
2025-07-05 05:03:01.354 [Debug] VocomService: WiFi is available
2025-07-05 05:03:01.354 [Information] VocomService: Found 3 Vocom devices
2025-07-05 05:03:01.354 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-05 05:04:21.865 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 05:04:21.865 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-05 05:04:21.866 [Information] VocomService: Using new enhanced device detection service
2025-07-05 05:04:21.866 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-05 05:04:21.866 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-05 05:04:22.028 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-05 05:04:22.028 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-05 05:04:22.028 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-05 05:04:22.029 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-05 05:04:22.029 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-05 05:04:22.133 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-05 05:04:22.133 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-05 05:04:22.134 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-05 05:04:22.134 [Information] MainViewModel: Found 1 Vocom device(s)
