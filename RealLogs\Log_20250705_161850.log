Log started at 7/5/2025 4:18:50 PM
2025-07-05 16:18:50.225 [Information] LoggingService: Logging service initialized
2025-07-05 16:18:50.241 [Information] App: Starting integrated application initialization
2025-07-05 16:18:50.242 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 16:18:50.245 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 16:18:50.248 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 16:18:50.249 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 16:18:50.251 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 16:18:50.253 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 16:18:50.256 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 16:18:50.269 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 16:18:50.274 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:18:50.281 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:18:50.288 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:18:50.290 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 16:18:50.292 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 16:18:50.292 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 16:18:50.294 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 16:18:50.294 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 16:18:50.295 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 16:18:50.295 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 16:18:50.296 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 16:18:50.296 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 16:18:50.297 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 16:18:50.298 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 16:18:50.298 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:18:50.298 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:18:50.299 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:18:50.306 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 16:18:50.307 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 16:18:50.307 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 16:18:50.315 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 16:18:50.315 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 16:18:50.317 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 16:18:50.319 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 16:18:50.323 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 16:18:50.325 [Information] LibraryExtractor: Copying system libraries
2025-07-05 16:18:50.332 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 16:18:50.341 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 16:19:14.532 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 16:20:02.952 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 16:21:50.235 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 16:21:50.236 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 16:21:50.237 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 16:21:50.237 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 16:21:50.237 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 16:21:50.238 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 16:21:50.243 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 16:21:50.245 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 16:21:50.247 [Information] DependencyManager: Initializing dependency manager
2025-07-05 16:21:50.249 [Information] DependencyManager: Setting up library search paths
2025-07-05 16:21:50.250 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:21:50.250 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:21:50.251 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 16:21:50.251 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 16:21:50.253 [Information] DependencyManager: Verifying required directories
2025-07-05 16:21:50.253 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:21:50.254 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:21:50.254 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 16:21:50.255 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 16:21:50.256 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 16:21:50.264 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 16:21:50.266 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 16:21:50.268 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 16:21:50.270 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 16:21:50.271 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 16:21:50.272 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:21:50.273 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:21:50.274 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:21:50.275 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 16:21:50.277 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 16:21:50.279 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 16:21:50.280 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 16:21:50.281 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 16:21:50.282 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 16:21:50.282 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 16:21:50.283 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 16:21:50.285 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 16:21:50.286 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 16:21:50.287 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 16:21:50.287 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 16:21:50.288 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 16:21:50.289 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 16:21:50.289 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 16:21:50.290 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 16:21:50.291 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 16:21:50.292 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 16:21:50.293 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 16:21:50.294 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 16:21:50.295 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 16:21:50.295 [Information] DependencyManager: Setting up environment variables
2025-07-05 16:21:50.296 [Information] DependencyManager: Environment variables configured
2025-07-05 16:21:50.297 [Information] DependencyManager: Verifying library loading status
2025-07-05 16:21:50.622 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 16:21:50.623 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 16:21:50.624 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 16:21:50.627 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 16:21:50.628 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 16:21:50.632 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 16:21:50.634 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 16:21:50.634 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 16:21:50.635 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 16:21:50.636 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 16:21:50.636 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 16:21:50.637 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:21:50.637 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:21:50.637 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 16:21:50.638 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 16:21:50.638 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 16:21:50.638 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:21:50.639 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 16:21:50.639 [Information] App: Integrated startup completed successfully
2025-07-05 16:21:50.643 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 16:21:50.793 [Information] App: Initializing application services
2025-07-05 16:21:50.795 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 16:21:50.795 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 16:21:50.838 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 16:21:50.839 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 16:21:50.840 [Information] App: Configuration service initialized successfully
2025-07-05 16:21:50.842 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 16:21:50.843 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 16:21:50.847 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 16:21:50.848 [Information] App: Final useDummyImplementations value: False
2025-07-05 16:21:50.848 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 16:21:50.864 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 16:21:50.865 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 16:21:50.865 [Information] App: usePatchedImplementation flag is: True
2025-07-05 16:21:50.866 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 16:21:50.866 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 16:21:50.866 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 16:21:50.867 [Information] App: verboseLogging flag is: True
2025-07-05 16:21:50.869 [Information] App: Verifying real hardware requirements...
2025-07-05 16:21:50.869 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 16:21:50.870 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 16:21:50.870 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 16:21:50.870 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 16:21:50.871 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 16:21:50.871 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 16:21:50.871 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 16:21:50.872 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 16:21:50.883 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 16:21:50.885 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 16:21:50.887 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 16:21:50.889 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 16:21:50.890 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 16:21:50.891 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 16:21:50.891 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 16:21:50.892 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 16:21:50.892 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 16:21:50.892 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 16:21:50.893 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 16:21:50.893 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 16:21:50.894 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 16:21:50.895 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-05 16:21:50.897 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-05 16:21:50.898 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-05 16:21:50.898 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-05 16:21:50.898 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-05 16:21:50.900 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 16:21:50.901 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 16:21:50.902 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 16:21:50.903 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-05 16:21:50.906 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-05 16:21:50.911 [Error] VocomArchitectureBridge: Bridge executable not found at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Bridge\VolvoFlashWR.VocomBridge.exe
2025-07-05 16:21:50.911 [Error] VocomArchitectureBridge: Failed to start bridge process
2025-07-05 16:21:50.912 [Error] BridgedVocomService: Failed to initialize architecture bridge
2025-07-05 16:21:50.912 [Warning] App: Architecture-aware Vocom service initialization failed
2025-07-05 16:21:50.916 [Error] App: Error initializing application: Object reference not set to an instance of an object.
2025-07-05 16:21:50.933 [Error] App: Stack trace:    at VolvoFlashWR.UI.App.InitializeServicesAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\App.xaml.cs:line 434
   at VolvoFlashWR.UI.App.<>c__DisplayClass11_0.<<OnStartup>b__0>d.MoveNext() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\App.xaml.cs:line 75
