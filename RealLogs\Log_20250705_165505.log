Log started at 7/5/2025 4:55:05 PM
2025-07-05 16:55:05.772 [Information] LoggingService: Logging service initialized
2025-07-05 16:55:05.790 [Information] App: Starting integrated application initialization
2025-07-05 16:55:05.792 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 16:55:05.796 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 16:55:05.798 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 16:55:05.799 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 16:55:05.801 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 16:55:05.803 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 16:55:05.806 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 16:55:05.823 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 16:55:05.833 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:55:05.842 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:55:05.853 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:55:05.856 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 16:55:05.859 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 16:55:05.860 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 16:55:05.862 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 16:55:05.862 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 16:55:05.863 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 16:55:05.864 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 16:55:05.864 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 16:55:05.865 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 16:55:05.866 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 16:55:05.866 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 16:55:05.867 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:55:05.867 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:55:05.868 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:55:05.876 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 16:55:05.877 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 16:55:05.877 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 16:55:05.884 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 16:55:05.884 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 16:55:05.886 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 16:55:05.888 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 16:55:05.892 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 16:55:05.895 [Information] LibraryExtractor: Copying system libraries
2025-07-05 16:55:05.902 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 16:55:05.912 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 16:55:31.185 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 16:56:19.164 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 16:57:50.395 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 16:57:50.396 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 16:57:50.396 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 16:57:50.396 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 16:57:50.397 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 16:57:50.398 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 16:57:50.403 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 16:57:50.405 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 16:57:50.407 [Information] DependencyManager: Initializing dependency manager
2025-07-05 16:57:50.408 [Information] DependencyManager: Setting up library search paths
2025-07-05 16:57:50.409 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:57:50.410 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:57:50.410 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 16:57:50.410 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 16:57:50.412 [Information] DependencyManager: Verifying required directories
2025-07-05 16:57:50.413 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:57:50.413 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:57:50.413 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 16:57:50.414 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 16:57:50.415 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 16:57:50.424 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 16:57:50.425 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 16:57:50.428 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 16:57:50.431 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 16:57:50.433 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 16:57:50.434 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:57:50.435 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:57:50.436 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:57:50.438 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 16:57:50.439 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 16:57:50.440 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 16:57:50.440 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 16:57:50.441 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 16:57:50.442 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 16:57:50.443 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 16:57:50.443 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 16:57:50.444 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 16:57:50.445 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 16:57:50.445 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 16:57:50.446 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 16:57:50.447 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 16:57:50.447 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 16:57:50.448 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 16:57:50.450 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 16:57:50.451 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 16:57:50.452 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 16:57:50.453 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 16:57:50.454 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 16:57:50.454 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 16:57:50.455 [Information] DependencyManager: Setting up environment variables
2025-07-05 16:57:50.456 [Information] DependencyManager: Environment variables configured
2025-07-05 16:57:50.457 [Information] DependencyManager: Verifying library loading status
2025-07-05 16:57:50.815 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 16:57:50.816 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 16:57:50.817 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 16:57:50.820 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 16:57:50.822 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 16:57:50.826 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 16:57:50.828 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 16:57:50.829 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 16:57:50.829 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 16:57:50.831 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 16:57:50.832 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 16:57:50.833 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:57:50.833 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:57:50.834 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 16:57:50.834 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 16:57:50.834 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 16:57:50.835 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:57:50.835 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 16:57:50.835 [Information] App: Integrated startup completed successfully
2025-07-05 16:57:50.839 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 16:57:51.075 [Information] App: Initializing application services
2025-07-05 16:57:51.078 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 16:57:51.078 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 16:57:51.138 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 16:57:51.139 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 16:57:51.141 [Information] App: Configuration service initialized successfully
2025-07-05 16:57:51.143 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 16:57:51.143 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 16:57:51.152 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 16:57:51.152 [Information] App: Final useDummyImplementations value: False
2025-07-05 16:57:51.153 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 16:57:51.172 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 16:57:51.173 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 16:57:51.173 [Information] App: usePatchedImplementation flag is: True
2025-07-05 16:57:51.174 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 16:57:51.174 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 16:57:51.174 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 16:57:51.175 [Information] App: verboseLogging flag is: True
2025-07-05 16:57:51.177 [Information] App: Verifying real hardware requirements...
2025-07-05 16:57:51.178 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 16:57:51.178 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 16:57:51.178 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 16:57:51.179 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 16:57:51.179 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 16:57:51.180 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 16:57:51.180 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 16:57:51.181 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 16:57:51.192 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 16:57:51.194 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 16:57:51.196 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 16:57:51.199 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 16:57:51.200 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 16:57:51.201 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 16:57:51.201 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 16:57:51.202 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 16:57:51.202 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 16:57:51.202 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 16:57:51.203 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 16:57:51.203 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 16:57:51.204 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 16:57:51.205 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-05 16:57:51.207 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-05 16:57:51.208 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-05 16:57:51.208 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-05 16:57:51.208 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-05 16:57:51.210 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 16:57:51.210 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 16:57:51.211 [Warning] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - bridge not yet implemented, falling back to dummy mode
2025-07-05 16:57:51.213 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 16:57:51.215 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 16:57:51.215 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 16:57:51.216 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-05 16:57:51.216 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-05 16:57:51.217 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 16:57:51.257 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 16:57:51.358 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 16:57:51.359 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 16:57:51.360 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 16:57:51.562 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 16:57:51.562 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-05 16:57:51.568 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 16:57:51.571 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:51.572 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 16:57:51.578 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 16:57:51.580 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 16:57:51.581 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 16:57:51.585 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 16:57:51.587 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 16:57:51.590 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 16:57:51.593 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 16:57:51.595 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 16:57:51.604 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 16:57:51.607 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 16:57:51.619 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 16:57:51.620 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 16:57:51.621 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 16:57:51.621 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 16:57:51.621 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 16:57:51.622 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 16:57:51.622 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 16:57:51.622 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 16:57:51.623 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 16:57:51.625 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 16:57:51.625 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 16:57:51.626 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 16:57:51.626 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 16:57:51.626 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 16:57:51.627 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 16:57:51.627 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 16:57:51.627 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 16:57:51.631 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 16:57:51.638 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 16:57:51.639 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 16:57:51.643 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 16:57:51.645 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:51.651 [Information] CANRegisterAccess: Read value 0x34 from register 0x0141 (simulated)
2025-07-05 16:57:51.658 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:51.664 [Information] CANRegisterAccess: Read value 0x64 from register 0x0141 (simulated)
2025-07-05 16:57:51.670 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:51.676 [Information] CANRegisterAccess: Read value 0x56 from register 0x0141 (simulated)
2025-07-05 16:57:51.683 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:51.689 [Information] CANRegisterAccess: Read value 0x36 from register 0x0141 (simulated)
2025-07-05 16:57:51.695 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:51.701 [Information] CANRegisterAccess: Read value 0xF7 from register 0x0141 (simulated)
2025-07-05 16:57:51.702 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 16:57:51.703 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 16:57:51.703 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 16:57:51.709 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 16:57:51.710 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 16:57:51.716 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 16:57:51.716 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 16:57:51.717 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 16:57:51.723 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 16:57:51.724 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 16:57:51.724 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 16:57:51.730 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 16:57:51.731 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 16:57:51.737 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 16:57:51.737 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 16:57:51.743 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 16:57:51.744 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 16:57:51.749 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 16:57:51.750 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 16:57:51.756 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 16:57:51.757 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 16:57:51.763 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 16:57:51.764 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 16:57:51.770 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 16:57:51.771 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 16:57:51.776 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 16:57:51.777 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 16:57:51.783 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 16:57:51.784 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 16:57:51.789 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 16:57:51.790 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 16:57:51.795 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 16:57:51.796 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 16:57:51.801 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 16:57:51.802 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 16:57:51.807 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 16:57:51.808 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 16:57:51.813 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 16:57:51.814 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 16:57:51.819 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 16:57:51.820 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 16:57:51.825 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 16:57:51.826 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 16:57:51.831 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 16:57:51.833 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 16:57:51.833 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 16:57:51.839 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 16:57:51.840 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 16:57:51.840 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 16:57:51.840 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:51.846 [Information] CANRegisterAccess: Read value 0x5E from register 0x0141 (simulated)
2025-07-05 16:57:51.847 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 16:57:51.847 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 16:57:51.848 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 16:57:51.848 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 16:57:51.854 [Information] CANRegisterAccess: Read value 0x9E from register 0x0140 (simulated)
2025-07-05 16:57:51.855 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 16:57:51.855 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 16:57:51.859 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 16:57:51.860 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 16:57:51.871 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 16:57:51.872 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 16:57:51.873 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 16:57:51.876 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 16:57:52.028 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 16:57:52.029 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 16:57:52.030 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 16:57:52.033 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 16:57:52.034 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 16:57:52.045 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 16:57:52.046 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 16:57:52.046 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 16:57:52.057 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 16:57:52.068 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 16:57:52.079 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 16:57:52.089 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 16:57:52.136 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 16:57:52.175 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 16:57:52.176 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 16:57:52.207 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 16:57:52.210 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 16:57:52.213 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 16:57:52.227 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 16:57:52.243 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 16:57:52.254 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 16:57:52.265 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 16:57:52.277 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 16:57:52.288 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 16:57:52.290 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 16:57:52.291 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 16:57:52.302 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 16:57:52.303 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 16:57:52.304 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 16:57:52.304 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 16:57:52.304 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 16:57:52.305 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 16:57:52.305 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 16:57:52.305 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 16:57:52.306 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 16:57:52.306 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 16:57:52.306 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 16:57:52.306 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 16:57:52.307 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 16:57:52.307 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 16:57:52.307 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 16:57:52.308 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 16:57:52.308 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 16:57:52.409 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 16:57:52.409 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 16:57:52.413 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 16:57:52.414 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:52.415 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 16:57:52.416 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 16:57:52.417 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:52.417 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 16:57:52.418 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 16:57:52.418 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:52.419 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 16:57:52.419 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 16:57:52.419 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:52.420 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 16:57:52.420 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 16:57:52.421 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 16:57:52.426 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 16:57:52.428 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 16:57:52.431 [Information] BackupService: Initializing backup service
2025-07-05 16:57:52.431 [Information] BackupService: Backup service initialized successfully
2025-07-05 16:57:52.432 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 16:57:52.432 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 16:57:52.436 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 16:57:52.473 [Information] BackupService: Compressing backup data
2025-07-05 16:57:52.481 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-05 16:57:52.485 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 16:57:52.486 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 16:57:52.489 [Information] BackupService: Compressing backup data
2025-07-05 16:57:52.491 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-05 16:57:52.492 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 16:57:52.492 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 16:57:52.493 [Information] BackupService: Compressing backup data
2025-07-05 16:57:52.495 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-05 16:57:52.495 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 16:57:52.496 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 16:57:52.496 [Information] BackupService: Compressing backup data
2025-07-05 16:57:52.497 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (448 bytes)
2025-07-05 16:57:52.498 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 16:57:52.498 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 16:57:52.501 [Information] BackupService: Compressing backup data
2025-07-05 16:57:52.503 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-05 16:57:52.503 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 16:57:52.504 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 16:57:52.504 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 16:57:52.505 [Information] BackupService: Compressing backup data
2025-07-05 16:57:52.506 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-05 16:57:52.506 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 16:57:52.507 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 16:57:52.509 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 16:57:52.512 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 16:57:52.515 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 16:57:52.604 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 16:57:52.605 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 16:57:52.607 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 16:57:52.607 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 16:57:52.608 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 16:57:52.609 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 16:57:52.610 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 16:57:52.615 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 16:57:52.618 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 16:57:52.629 [Information] LicensingService: Initializing licensing service
2025-07-05 16:57:52.686 [Information] LicensingService: License information loaded successfully
2025-07-05 16:57:52.688 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 16:57:52.689 [Information] App: Licensing service initialized successfully
2025-07-05 16:57:52.689 [Information] App: License status: Trial
2025-07-05 16:57:52.689 [Information] App: Trial period: 30 days remaining
2025-07-05 16:57:52.690 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 16:57:52.869 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 16:57:52.869 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 16:57:52.919 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 16:57:52.920 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 16:57:52.920 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 16:57:52.921 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 16:57:52.921 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 16:57:52.922 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 16:57:52.923 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 16:57:52.924 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 16:57:52.925 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 16:57:52.925 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 16:57:52.936 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 16:57:52.937 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 16:57:52.937 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 16:57:52.938 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 16:57:52.938 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 16:57:52.938 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 16:57:52.938 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 16:57:52.939 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 16:57:52.939 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 16:57:52.939 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 16:57:52.940 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 16:57:52.940 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 16:57:52.940 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 16:57:52.940 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 16:57:52.941 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 16:57:52.941 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 16:57:52.941 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 16:57:52.942 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 16:57:52.945 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 16:57:52.946 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 16:57:52.946 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 16:57:52.946 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:52.952 [Information] CANRegisterAccess: Read value 0x48 from register 0x0141 (simulated)
2025-07-05 16:57:52.958 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:52.964 [Information] CANRegisterAccess: Read value 0xA8 from register 0x0141 (simulated)
2025-07-05 16:57:52.970 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:52.976 [Information] CANRegisterAccess: Read value 0x30 from register 0x0141 (simulated)
2025-07-05 16:57:52.983 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:52.989 [Information] CANRegisterAccess: Read value 0x0E from register 0x0141 (simulated)
2025-07-05 16:57:52.995 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:53.001 [Information] CANRegisterAccess: Read value 0x4A from register 0x0141 (simulated)
2025-07-05 16:57:53.007 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:53.013 [Information] CANRegisterAccess: Read value 0x23 from register 0x0141 (simulated)
2025-07-05 16:57:53.014 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 16:57:53.014 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 16:57:53.014 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 16:57:53.020 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 16:57:53.021 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 16:57:53.026 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 16:57:53.027 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 16:57:53.027 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 16:57:53.033 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 16:57:53.034 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 16:57:53.034 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 16:57:53.040 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 16:57:53.041 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 16:57:53.047 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 16:57:53.048 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 16:57:53.054 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 16:57:53.055 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 16:57:53.061 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 16:57:53.062 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 16:57:53.068 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 16:57:53.069 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 16:57:53.075 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 16:57:53.076 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 16:57:53.081 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 16:57:53.082 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 16:57:53.088 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 16:57:53.089 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 16:57:53.094 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 16:57:53.095 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 16:57:53.101 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 16:57:53.102 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 16:57:53.108 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 16:57:53.109 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 16:57:53.115 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 16:57:53.116 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 16:57:53.123 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 16:57:53.124 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 16:57:53.130 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 16:57:53.131 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 16:57:53.137 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 16:57:53.138 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 16:57:53.144 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 16:57:53.145 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 16:57:53.152 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 16:57:53.152 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 16:57:53.153 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 16:57:53.158 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 16:57:53.159 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 16:57:53.160 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 16:57:53.160 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 16:57:53.168 [Information] CANRegisterAccess: Read value 0x42 from register 0x0141 (simulated)
2025-07-05 16:57:53.169 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 16:57:53.169 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 16:57:53.169 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 16:57:53.170 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 16:57:53.175 [Information] CANRegisterAccess: Read value 0x8F from register 0x0140 (simulated)
2025-07-05 16:57:53.181 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 16:57:53.187 [Information] CANRegisterAccess: Read value 0x70 from register 0x0140 (simulated)
2025-07-05 16:57:53.188 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 16:57:53.188 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 16:57:53.193 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 16:57:53.194 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 16:57:53.205 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 16:57:53.206 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 16:57:53.206 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 16:57:53.206 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 16:57:53.357 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 16:57:53.358 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 16:57:53.358 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 16:57:53.359 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 16:57:53.360 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 16:57:53.371 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 16:57:53.371 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 16:57:53.372 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 16:57:53.386 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 16:57:53.397 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 16:57:53.408 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 16:57:53.419 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 16:57:53.429 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 16:57:53.430 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 16:57:53.430 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 16:57:53.441 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 16:57:53.442 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 16:57:53.442 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 16:57:53.453 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 16:57:53.464 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 16:57:53.475 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 16:57:53.486 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 16:57:53.497 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 16:57:53.508 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 16:57:53.509 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 16:57:53.509 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 16:57:53.520 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 16:57:53.520 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 16:57:53.521 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 16:57:53.521 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 16:57:53.522 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 16:57:53.522 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 16:57:53.522 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 16:57:53.522 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 16:57:53.523 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 16:57:53.523 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 16:57:53.523 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 16:57:53.524 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 16:57:53.524 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 16:57:53.524 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 16:57:53.525 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 16:57:53.525 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 16:57:53.525 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 16:57:53.625 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 16:57:53.626 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 16:57:53.626 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 16:57:53.627 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:53.627 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 16:57:53.628 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 16:57:53.628 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:53.628 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 16:57:53.628 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 16:57:53.629 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:53.629 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 16:57:53.629 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 16:57:53.630 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 16:57:53.630 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 16:57:53.630 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 16:57:53.682 [Information] BackupService: Initializing backup service
2025-07-05 16:57:53.682 [Information] BackupService: Backup service initialized successfully
2025-07-05 16:57:53.734 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 16:57:53.734 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 16:57:53.736 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 16:57:53.737 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 16:57:53.790 [Information] BackupService: Getting predefined backup categories
2025-07-05 16:57:53.842 [Information] MainViewModel: Services initialized successfully
2025-07-05 16:57:53.844 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 16:57:53.846 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 16:57:53.947 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 16:57:53.949 [Information] MainViewModel: Found 1 Vocom device(s)
