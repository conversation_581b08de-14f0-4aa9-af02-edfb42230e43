using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Automatically extracts and manages embedded libraries and dependencies
    /// Ensures all required libraries are available for Vocom communication
    /// </summary>
    public class LibraryExtractor
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly string _driversPath;

        // Embedded Visual C++ Redistributable libraries (base64 encoded or as resources)
        private static readonly Dictionary<string, string> EmbeddedLibraries = new()
        {
            // These would be embedded as resources in the application
            // For now, we'll use download URLs as fallback
        };

        // Download URLs for critical libraries (fallback)
        private static readonly Dictionary<string, string> LibraryDownloadUrls = new()
        {
            ["msvcr120.dll"] = "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe",
            ["msvcr140.dll"] = "https://aka.ms/vs/17/release/vc_redist.x86.exe",
            ["msvcp140.dll"] = "https://aka.ms/vs/17/release/vc_redist.x86.exe",
            ["vcruntime140.dll"] = "https://aka.ms/vs/17/release/vc_redist.x86.exe",
            ["api-ms-win-crt-runtime-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x86.exe",
            ["api-ms-win-crt-heap-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x86.exe",
            ["api-ms-win-crt-string-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x86.exe"
        };

        // Critical library information
        private static readonly Dictionary<string, LibraryInfo> CriticalLibraryInfo = new()
        {
            ["WUDFPuma.dll"] = new LibraryInfo
            {
                Name = "WUDFPuma.dll",
                Description = "Vocom 1 Adapter Driver",
                IsRequired = true,
                SourcePaths = new[]
                {
                    @"C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll",
                    @"C:\Windows\System32\drivers\UMDF\WUDFPuma.dll"
                }
            },
            ["apci.dll"] = new LibraryInfo
            {
                Name = "apci.dll",
                Description = "APCI Communication Library",
                IsRequired = true,
                SourcePaths = new[]
                {
                    @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\apci.dll",
                    @"C:\Program Files\Phoenix Diag\Flash Editor Plus 2021\apci.dll"
                }
            },
            ["Volvo.ApciPlus.dll"] = new LibraryInfo
            {
                Name = "Volvo.ApciPlus.dll",
                Description = "Volvo APCI Plus Communication",
                IsRequired = true,
                SourcePaths = new[]
                {
                    @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll",
                    @"C:\Program Files\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll"
                }
            }
        };

        public LibraryExtractor(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _driversPath = Path.Combine(_applicationPath, "Drivers", "Vocom");
        }

        /// <summary>
        /// Extracts and ensures all required libraries are available
        /// </summary>
        public async Task<bool> ExtractLibrariesAsync()
        {
            try
            {
                _logger.LogInformation("Starting library extraction process", "LibraryExtractor");

                // Create required directories
                await CreateDirectoriesAsync();

                // Extract embedded libraries
                await ExtractEmbeddedLibrariesAsync();

                // Copy system libraries
                await CopySystemLibrariesAsync();

                // Download missing critical libraries
                await DownloadMissingLibrariesAsync();

                // Verify extraction results
                bool success = await VerifyExtractionAsync();

                _logger.LogInformation($"Library extraction completed. Success: {success}", "LibraryExtractor");
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during library extraction: {ex.Message}", "LibraryExtractor", ex);
                return false;
            }
        }

        private async Task CreateDirectoriesAsync()
        {
            var directories = new[]
            {
                _librariesPath,
                _driversPath,
                Path.Combine(_librariesPath, "System"),
                Path.Combine(_librariesPath, "VCRedist"),
                Path.Combine(_driversPath, "Backup")
            };

            foreach (string directory in directories)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    _logger.LogInformation($"Created directory: {directory}", "LibraryExtractor");
                }
            }

            await Task.CompletedTask;
        }

        private async Task ExtractEmbeddedLibrariesAsync()
        {
            _logger.LogInformation("Extracting embedded libraries", "LibraryExtractor");

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();

            foreach (string resourceName in resourceNames)
            {
                if (resourceName.EndsWith(".dll") || resourceName.EndsWith(".exe"))
                {
                    try
                    {
                        string fileName = Path.GetFileName(resourceName);
                        string targetPath = Path.Combine(_librariesPath, fileName);

                        if (!File.Exists(targetPath))
                        {
                            using var stream = assembly.GetManifestResourceStream(resourceName);
                            if (stream != null)
                            {
                                using var fileStream = File.Create(targetPath);
                                await stream.CopyToAsync(fileStream);
                                _logger.LogInformation($"Extracted embedded library: {fileName}", "LibraryExtractor");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to extract embedded resource {resourceName}: {ex.Message}", "LibraryExtractor");
                    }
                }
            }
        }

        private async Task CopySystemLibrariesAsync()
        {
            _logger.LogInformation("Copying system libraries", "LibraryExtractor");

            foreach (var libraryInfo in CriticalLibraryInfo.Values)
            {
                string targetPath = Path.Combine(_librariesPath, libraryInfo.Name);

                if (!File.Exists(targetPath))
                {
                    bool copied = false;
                    foreach (string sourcePath in libraryInfo.SourcePaths)
                    {
                        if (File.Exists(sourcePath))
                        {
                            try
                            {
                                File.Copy(sourcePath, targetPath, true);
                                _logger.LogInformation($"Copied system library: {libraryInfo.Name} from {sourcePath}", "LibraryExtractor");
                                copied = true;
                                break;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to copy {libraryInfo.Name} from {sourcePath}: {ex.Message}", "LibraryExtractor");
                            }
                        }
                    }

                    if (!copied && libraryInfo.IsRequired)
                    {
                        _logger.LogWarning($"Required library not found in system: {libraryInfo.Name}", "LibraryExtractor");
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task DownloadMissingLibrariesAsync()
        {
            _logger.LogInformation("Checking for missing libraries to download", "LibraryExtractor");

            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5);

            foreach (var kvp in LibraryDownloadUrls)
            {
                string libraryName = kvp.Key;
                string downloadUrl = kvp.Value;
                string targetPath = Path.Combine(_librariesPath, libraryName);

                if (!File.Exists(targetPath))
                {
                    try
                    {
                        _logger.LogInformation($"Downloading missing library: {libraryName}", "LibraryExtractor");

                        // For redistributable packages, we need to extract the actual DLLs
                        if (downloadUrl.EndsWith(".exe"))
                        {
                            await DownloadAndExtractRedistributableAsync(httpClient, downloadUrl, libraryName);
                        }
                        else
                        {
                            var response = await httpClient.GetAsync(downloadUrl);
                            response.EnsureSuccessStatusCode();

                            using var fileStream = File.Create(targetPath);
                            await response.Content.CopyToAsync(fileStream);

                            _logger.LogInformation($"Downloaded library: {libraryName}", "LibraryExtractor");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to download library {libraryName}: {ex.Message}", "LibraryExtractor");
                    }
                }
            }
        }

        private async Task DownloadAndExtractRedistributableAsync(HttpClient httpClient, string downloadUrl, string targetLibrary)
        {
            try
            {
                string tempPath = Path.Combine(Path.GetTempPath(), $"vcredist_{Guid.NewGuid()}.exe");
                string extractPath = Path.Combine(Path.GetTempPath(), $"vcredist_extract_{Guid.NewGuid()}");

                // Download the redistributable
                var response = await httpClient.GetAsync(downloadUrl);
                response.EnsureSuccessStatusCode();

                using (var fileStream = File.Create(tempPath))
                {
                    await response.Content.CopyToAsync(fileStream);
                }

                // Extract the redistributable (this is a simplified approach)
                // In a real implementation, you would use proper extraction tools
                Directory.CreateDirectory(extractPath);

                // Try to extract using built-in Windows tools
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempPath,
                    Arguments = $"/extract:{extractPath} /quiet",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();

                    // Look for the target library in extracted files
                    var extractedFiles = Directory.GetFiles(extractPath, "*.dll", SearchOption.AllDirectories);
                    foreach (string file in extractedFiles)
                    {
                        if (Path.GetFileName(file).Equals(targetLibrary, StringComparison.OrdinalIgnoreCase))
                        {
                            string targetPath = Path.Combine(_librariesPath, targetLibrary);
                            File.Copy(file, targetPath, true);
                            _logger.LogInformation($"Extracted and copied: {targetLibrary}", "LibraryExtractor");
                            break;
                        }
                    }
                }

                // Cleanup
                if (File.Exists(tempPath))
                    File.Delete(tempPath);
                if (Directory.Exists(extractPath))
                    Directory.Delete(extractPath, true);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to download and extract redistributable for {targetLibrary}: {ex.Message}", "LibraryExtractor");
            }
        }

        private async Task<bool> VerifyExtractionAsync()
        {
            _logger.LogInformation("Verifying library extraction", "LibraryExtractor");

            int foundCount = 0;
            int requiredCount = 0;

            foreach (var libraryInfo in CriticalLibraryInfo.Values)
            {
                if (libraryInfo.IsRequired)
                {
                    requiredCount++;
                    string targetPath = Path.Combine(_librariesPath, libraryInfo.Name);

                    if (File.Exists(targetPath))
                    {
                        foundCount++;
                        _logger.LogInformation($"✓ Verified: {libraryInfo.Name}", "LibraryExtractor");
                    }
                    else
                    {
                        _logger.LogWarning($"✗ Missing: {libraryInfo.Name}", "LibraryExtractor");
                    }
                }
            }

            double successRate = requiredCount > 0 ? (double)foundCount / requiredCount * 100 : 100;
            _logger.LogInformation($"Library verification: {foundCount}/{requiredCount} ({successRate:F1}%) required libraries found", "LibraryExtractor");

            await Task.CompletedTask;
            return successRate >= 80; // Consider successful if 80% or more libraries are found
        }

        /// <summary>
        /// Gets the extraction status
        /// </summary>
        public async Task<ExtractionStatus> GetStatusAsync()
        {
            var status = new ExtractionStatus
            {
                LibrariesPath = _librariesPath,
                DriversPath = _driversPath,
                AvailableLibraries = new List<string>(),
                MissingLibraries = new List<string>()
            };

            foreach (var libraryInfo in CriticalLibraryInfo.Values)
            {
                string targetPath = Path.Combine(_librariesPath, libraryInfo.Name);
                if (File.Exists(targetPath))
                {
                    var fileInfo = new FileInfo(targetPath);
                    status.AvailableLibraries.Add($"{libraryInfo.Name} ({fileInfo.Length} bytes, {fileInfo.LastWriteTime})");
                }
                else
                {
                    status.MissingLibraries.Add(libraryInfo.Name);
                }
            }

            await Task.CompletedTask;
            return status;
        }
    }

    /// <summary>
    /// Information about a library
    /// </summary>
    public class LibraryInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public string[] SourcePaths { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// Status of library extraction
    /// </summary>
    public class ExtractionStatus
    {
        public string LibrariesPath { get; set; } = string.Empty;
        public string DriversPath { get; set; } = string.Empty;
        public List<string> AvailableLibraries { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
    }
}
