using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Resolves missing runtime dependencies like Visual C++ redistributables
    /// </summary>
    public class RuntimeDependencyResolver
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;

        public RuntimeDependencyResolver(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = AppDomain.CurrentDomain.BaseDirectory;
        }

        /// <summary>
        /// Checks and resolves missing Visual C++ runtime dependencies
        /// </summary>
        public async Task<bool> ResolveVCRuntimeDependenciesAsync()
        {
            try
            {
                _logger.LogInformation("Checking Visual C++ runtime dependencies", "RuntimeDependencyResolver");

                // Check for missing msvcr140.dll
                if (!IsVCRuntimeAvailable("msvcr140.dll"))
                {
                    _logger.LogWarning("msvcr140.dll not found, attempting to resolve", "RuntimeDependencyResolver");
                    
                    // Try to copy from system directories
                    if (await TryCopyFromSystemDirectoriesAsync("msvcr140.dll"))
                    {
                        _logger.LogInformation("Successfully resolved msvcr140.dll from system", "RuntimeDependencyResolver");
                    }
                    else
                    {
                        _logger.LogWarning("Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed", "RuntimeDependencyResolver");
                        return false;
                    }
                }

                // Check other common VC runtime libraries
                string[] vcRuntimeLibraries = {
                    "msvcp140.dll",
                    "vcruntime140.dll",
                    "api-ms-win-crt-runtime-l1-1-0.dll",
                    "api-ms-win-crt-heap-l1-1-0.dll",
                    "api-ms-win-crt-string-l1-1-0.dll"
                };

                foreach (string library in vcRuntimeLibraries)
                {
                    if (!IsVCRuntimeAvailable(library))
                    {
                        _logger.LogInformation($"Attempting to resolve {library}", "RuntimeDependencyResolver");
                        await TryCopyFromSystemDirectoriesAsync(library);
                    }
                }

                _logger.LogInformation("Visual C++ runtime dependency resolution completed", "RuntimeDependencyResolver");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during VC runtime dependency resolution: {ex.Message}", "RuntimeDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Checks if a Visual C++ runtime library is available
        /// </summary>
        private bool IsVCRuntimeAvailable(string libraryName)
        {
            try
            {
                // Check in application directory
                string appPath = Path.Combine(_applicationPath, libraryName);
                if (File.Exists(appPath))
                {
                    return true;
                }

                // Check in Libraries subdirectory
                string libPath = Path.Combine(_applicationPath, "Libraries", libraryName);
                if (File.Exists(libPath))
                {
                    return true;
                }

                // Try to load from system
                IntPtr handle = LoadLibrary(libraryName);
                if (handle != IntPtr.Zero)
                {
                    FreeLibrary(handle);
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Attempts to copy a library from system directories
        /// </summary>
        private async Task<bool> TryCopyFromSystemDirectoriesAsync(string libraryName)
        {
            try
            {
                string[] systemPaths = {
                    Environment.GetFolderPath(Environment.SpecialFolder.System),
                    Environment.GetFolderPath(Environment.SpecialFolder.SystemX86),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "System32"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "SysWOW64")
                };

                foreach (string systemPath in systemPaths)
                {
                    string sourcePath = Path.Combine(systemPath, libraryName);
                    if (File.Exists(sourcePath))
                    {
                        try
                        {
                            string destinationPath = Path.Combine(_applicationPath, "Libraries", libraryName);
                            
                            // Ensure Libraries directory exists
                            Directory.CreateDirectory(Path.GetDirectoryName(destinationPath));
                            
                            await Task.Run(() => File.Copy(sourcePath, destinationPath, true));
                            
                            _logger.LogInformation($"Successfully copied {libraryName} from {sourcePath}", "RuntimeDependencyResolver");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Failed to copy {libraryName} from {sourcePath}: {ex.Message}", "RuntimeDependencyResolver");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception copying {libraryName}: {ex.Message}", "RuntimeDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Provides installation guidance for missing dependencies
        /// </summary>
        public void ProvideInstallationGuidance()
        {
            _logger.LogInformation("=== Visual C++ Runtime Installation Guidance ===", "RuntimeDependencyResolver");
            _logger.LogInformation("If you continue to experience issues with missing Visual C++ runtime libraries:", "RuntimeDependencyResolver");
            _logger.LogInformation("1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)", "RuntimeDependencyResolver");
            _logger.LogInformation("2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe", "RuntimeDependencyResolver");
            _logger.LogInformation("3. Restart the application after installation", "RuntimeDependencyResolver");
            _logger.LogInformation("=== End Installation Guidance ===", "RuntimeDependencyResolver");
        }

        #region P/Invoke Declarations

        [System.Runtime.InteropServices.DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [System.Runtime.InteropServices.DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        #endregion
    }
}
