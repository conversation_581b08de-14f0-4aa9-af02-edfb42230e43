﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\VolvoFlashWR.Core\VolvoFlashWR.Core.csproj" />
    <ProjectReference Include="..\VolvoFlashWR.Communication\VolvoFlashWR.Communication.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- UI and Charting -->
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WPF" Version="2.0.0-rc5.4" />

    <!-- WPF Extensions -->
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageReference Include="MaterialDesignThemes" Version="5.1.0" />

    <!-- Configuration and Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <!-- Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />

    <!-- JSON Processing -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Async and Threading -->
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
  </ItemGroup>

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <PlatformTarget>x86</PlatformTarget>
    <RuntimeIdentifier>win-x86</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
  </PropertyGroup>

</Project>
