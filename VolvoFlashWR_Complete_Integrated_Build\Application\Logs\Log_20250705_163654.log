Log started at 7/5/2025 4:36:54 PM
2025-07-05 16:36:54.563 [Information] LoggingService: Logging service initialized
2025-07-05 16:36:54.584 [Information] App: Starting integrated application initialization
2025-07-05 16:36:54.585 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 16:36:54.588 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 16:36:54.590 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 16:36:54.592 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 16:36:54.595 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 16:36:54.597 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 16:36:54.601 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 16:36:54.615 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 16:36:54.624 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:36:54.632 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:36:54.641 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:36:54.644 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 16:36:54.646 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 16:36:54.647 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 16:36:54.649 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 16:36:54.649 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 16:36:54.650 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 16:36:54.651 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 16:36:54.651 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 16:36:54.652 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 16:36:54.653 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 16:36:54.653 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 16:36:54.654 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:36:54.654 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:36:54.655 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:36:54.663 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 16:36:54.664 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 16:36:54.664 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 16:36:54.672 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 16:36:54.672 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 16:36:54.674 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 16:36:54.676 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 16:36:54.680 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 16:36:54.683 [Information] LibraryExtractor: Copying system libraries
2025-07-05 16:36:54.689 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 16:36:54.698 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 16:43:34.838 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 16:44:30.074 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 16:45:48.676 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 16:45:48.677 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 16:45:48.677 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 16:45:48.678 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 16:45:48.678 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 16:45:48.678 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 16:45:48.683 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 16:45:48.686 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 16:45:48.687 [Information] DependencyManager: Initializing dependency manager
2025-07-05 16:45:48.689 [Information] DependencyManager: Setting up library search paths
2025-07-05 16:45:48.690 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:45:48.690 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:45:48.691 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 16:45:48.691 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 16:45:48.693 [Information] DependencyManager: Verifying required directories
2025-07-05 16:45:48.693 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:45:48.694 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:45:48.694 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 16:45:48.694 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 16:45:48.696 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 16:45:48.704 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 16:45:48.706 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 16:45:48.709 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 16:45:48.712 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 16:45:48.713 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 16:45:48.714 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 16:45:48.715 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 16:45:48.716 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 16:45:48.718 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 16:45:48.719 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 16:45:48.720 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 16:45:48.721 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 16:45:48.722 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 16:45:48.722 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 16:45:48.723 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 16:45:48.724 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 16:45:48.724 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 16:45:48.725 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 16:45:48.726 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 16:45:48.726 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 16:45:48.727 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 16:45:48.728 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 16:45:48.728 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 16:45:48.729 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 16:45:48.730 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 16:45:48.731 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 16:45:48.732 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 16:45:48.733 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 16:45:48.734 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 16:45:48.735 [Information] DependencyManager: Setting up environment variables
2025-07-05 16:45:48.735 [Information] DependencyManager: Environment variables configured
2025-07-05 16:45:48.737 [Information] DependencyManager: Verifying library loading status
2025-07-05 16:45:49.077 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 16:45:49.078 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 16:45:49.079 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 16:45:49.082 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 16:45:49.084 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 16:45:49.088 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 16:45:49.090 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 16:45:49.091 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 16:45:49.091 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 16:45:49.093 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 16:45:49.093 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 16:45:49.094 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:45:49.094 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 16:45:49.094 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 16:45:49.095 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 16:45:49.095 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 16:45:49.096 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 16:45:49.099 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 16:45:49.100 [Information] App: Integrated startup completed successfully
2025-07-05 16:45:49.102 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 16:45:49.264 [Information] App: Initializing application services
2025-07-05 16:45:49.266 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 16:45:49.267 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 16:45:49.312 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 16:45:49.313 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 16:45:49.314 [Information] App: Configuration service initialized successfully
2025-07-05 16:45:49.316 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 16:45:49.316 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 16:45:49.323 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 16:45:49.324 [Information] App: Final useDummyImplementations value: False
2025-07-05 16:45:49.324 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 16:45:49.341 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 16:45:49.342 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 16:45:49.342 [Information] App: usePatchedImplementation flag is: True
2025-07-05 16:45:49.342 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 16:45:49.342 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 16:45:49.343 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 16:45:49.343 [Information] App: verboseLogging flag is: True
2025-07-05 16:45:49.345 [Information] App: Verifying real hardware requirements...
2025-07-05 16:45:49.346 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 16:45:49.347 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 16:45:49.347 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 16:45:49.347 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 16:45:49.348 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 16:45:49.348 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 16:45:49.349 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 16:45:49.349 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 16:45:49.360 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 16:45:49.363 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 16:45:49.365 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 16:45:49.368 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 16:45:49.368 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 16:45:49.369 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 16:45:49.369 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 16:45:49.370 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 16:45:49.370 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 16:45:49.370 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 16:45:49.371 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 16:45:49.371 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 16:45:49.372 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 16:45:49.373 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-05 16:45:49.375 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-05 16:45:49.375 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-05 16:45:49.376 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-05 16:45:49.376 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-05 16:45:49.377 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 16:45:49.378 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 16:45:49.378 [Warning] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - bridge not yet implemented, falling back to dummy mode
2025-07-05 16:45:49.381 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 16:45:49.382 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 16:45:49.383 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 16:45:49.383 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-05 16:45:49.385 [Error] App: Error initializing application: Object reference not set to an instance of an object.
2025-07-05 16:45:49.403 [Error] App: Stack trace:    at VolvoFlashWR.UI.App.InitializeServicesAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\App.xaml.cs:line 434
   at VolvoFlashWR.UI.App.<>c__DisplayClass11_0.<<OnStartup>b__0>d.MoveNext() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\App.xaml.cs:line 75
