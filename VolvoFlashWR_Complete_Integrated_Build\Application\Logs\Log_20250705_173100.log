Log started at 7/5/2025 5:31:00 PM
2025-07-05 17:31:00.710 [Information] LoggingService: Logging service initialized
2025-07-05 17:31:00.731 [Information] App: Starting integrated application initialization
2025-07-05 17:31:00.732 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 17:31:00.737 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 17:31:00.820 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 17:31:00.821 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 17:31:00.823 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 17:31:00.825 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 17:31:00.838 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 17:31:00.854 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 17:31:00.862 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 17:31:00.870 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 17:31:00.878 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 17:31:00.885 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 17:31:00.900 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 17:31:00.901 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 17:31:00.907 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 17:31:00.946 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 17:31:00.947 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 17:31:00.953 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 17:31:00.955 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 17:31:00.956 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 17:31:00.956 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 17:31:00.967 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 17:31:00.968 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 17:31:00.968 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 17:31:00.982 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 17:31:00.982 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 17:31:00.984 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 17:31:00.986 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 17:31:01.000 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 17:31:01.007 [Information] LibraryExtractor: Copying system libraries
2025-07-05 17:31:01.033 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 17:31:01.067 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 17:32:25.795 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 17:33:10.258 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
