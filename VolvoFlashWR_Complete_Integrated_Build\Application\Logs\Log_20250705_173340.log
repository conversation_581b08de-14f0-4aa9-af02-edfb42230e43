Log started at 7/5/2025 5:33:40 PM
2025-07-05 17:33:40.447 [Information] LoggingService: Logging service initialized
2025-07-05 17:33:40.466 [Information] App: Starting integrated application initialization
2025-07-05 17:33:40.468 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 17:33:40.472 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 17:33:40.483 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 17:33:40.483 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 17:33:40.485 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 17:33:40.488 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 17:33:40.498 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 17:33:40.510 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 17:33:40.517 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 17:33:40.525 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 17:33:40.533 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 17:33:40.540 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 17:33:40.548 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 17:33:40.548 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 17:33:40.551 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 17:33:40.554 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 17:33:40.554 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 17:33:40.556 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 17:33:40.558 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 17:33:40.558 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 17:33:40.559 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 17:33:40.570 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 17:33:40.570 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 17:33:40.571 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 17:33:40.584 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 17:33:40.585 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 17:33:40.587 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 17:33:40.589 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 17:33:40.602 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 17:33:40.612 [Information] LibraryExtractor: Copying system libraries
2025-07-05 17:33:40.631 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 17:33:40.638 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 17:33:54.604 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-05 17:34:18.040 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 17:35:06.902 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 17:35:54.180 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-05 17:36:44.689 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 17:38:31.027 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 17:39:20.158 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 17:40:10.355 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 17:40:10.356 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 17:40:10.357 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 17:40:10.357 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 17:40:10.357 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 17:40:10.358 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 17:40:10.369 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 17:40:10.372 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 17:40:10.374 [Information] DependencyManager: Initializing dependency manager
2025-07-05 17:40:10.375 [Information] DependencyManager: Setting up library search paths
2025-07-05 17:40:10.377 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 17:40:10.377 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 17:40:10.378 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 17:40:10.378 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 17:40:10.384 [Information] DependencyManager: Verifying required directories
2025-07-05 17:40:10.385 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 17:40:10.385 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 17:40:10.385 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 17:40:10.386 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 17:40:10.390 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 17:40:10.416 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 17:40:10.435 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 17:40:10.445 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 17:40:10.451 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 17:40:10.453 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 17:40:10.454 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 17:40:10.455 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 17:40:10.457 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 17:40:10.460 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 17:40:10.461 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-05 17:40:10.462 [Warning] DependencyManager: Failed to load Critical library WUDFPuma.dll: Error 193
2025-07-05 17:40:10.470 [Information] DependencyManager: ✓ Loaded Critical library: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll (x86)
2025-07-05 17:40:10.481 [Information] DependencyManager: ✓ Loaded Critical library: apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll (x86)
2025-07-05 17:40:10.483 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlus.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll (x86)
2025-07-05 17:40:10.484 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll (x86)
2025-07-05 17:40:10.485 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll (x86)
2025-07-05 17:40:10.486 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 17:40:10.487 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 17:40:10.488 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 17:40:10.490 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 17:40:10.491 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 17:40:10.492 [Information] DependencyManager: Setting up environment variables
2025-07-05 17:40:10.492 [Information] DependencyManager: Environment variables configured
2025-07-05 17:40:10.497 [Information] DependencyManager: Verifying library loading status
2025-07-05 17:40:11.023 [Information] DependencyManager: Library loading verification: 9/11 (81.8%) critical libraries loaded
2025-07-05 17:40:11.023 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 17:40:11.030 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 17:40:11.031 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 17:40:11.042 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 17:40:11.047 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 17:40:11.048 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 17:40:11.048 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 17:40:11.052 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 17:40:11.052 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 17:40:11.053 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 17:40:11.053 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 17:40:11.053 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 17:40:11.054 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 17:40:11.054 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 17:40:11.054 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 17:40:11.054 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 17:40:11.055 [Information] App: Integrated startup completed successfully
2025-07-05 17:40:11.059 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 17:40:11.259 [Information] App: Initializing application services
2025-07-05 17:40:11.261 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 17:40:11.261 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 17:40:11.325 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 17:40:11.326 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 17:40:11.327 [Information] App: Configuration service initialized successfully
2025-07-05 17:40:11.329 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 17:40:11.329 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 17:40:11.335 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 17:40:11.336 [Information] App: Final useDummyImplementations value: False
2025-07-05 17:40:11.336 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 17:40:11.353 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 17:40:11.354 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 17:40:11.356 [Information] App: usePatchedImplementation flag is: True
2025-07-05 17:40:11.358 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 17:40:11.358 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 17:40:11.358 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 17:40:11.359 [Information] App: verboseLogging flag is: True
2025-07-05 17:40:11.363 [Information] App: Verifying real hardware requirements...
2025-07-05 17:40:11.363 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 17:40:11.363 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 17:40:11.364 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 17:40:11.364 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 17:40:11.365 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 17:40:11.365 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 17:40:11.365 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 17:40:11.366 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 17:40:11.378 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 17:40:11.382 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 17:40:11.385 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 17:40:11.392 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 17:40:11.392 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 17:40:11.393 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 17:40:11.393 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 17:40:11.393 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 17:40:11.394 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 17:40:11.394 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 17:40:11.394 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 17:40:11.394 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 17:40:11.395 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 17:40:11.396 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x86
2025-07-05 17:40:11.402 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: apci.dll
2025-07-05 17:40:11.403 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: Volvo.ApciPlus.dll
2025-07-05 17:40:11.403 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: WUDFPuma.dll
2025-07-05 17:40:11.404 [Warning] ArchitectureAwareVocomServiceFactory: Found 1 incompatible libraries
2025-07-05 17:40:11.407 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 17:40:11.408 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 17:40:11.408 [Warning] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - bridge not yet implemented, falling back to dummy mode
2025-07-05 17:40:11.410 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 17:40:11.411 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 17:40:11.412 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 17:40:11.412 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-05 17:40:11.412 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-05 17:40:11.413 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 17:40:11.476 [Information] App: Skipping device scan for fast startup
2025-07-05 17:40:11.488 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 17:40:11.492 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:11.493 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 17:40:11.497 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 17:40:11.500 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 17:40:11.500 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 17:40:11.506 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 17:40:11.514 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 17:40:11.518 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 17:40:11.532 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 17:40:11.535 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 17:40:11.555 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 17:40:11.559 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 17:40:11.560 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:11.562 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 17:40:11.563 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 17:40:11.563 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:11.565 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 17:40:11.566 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 17:40:11.566 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:11.568 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 17:40:11.568 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 17:40:11.568 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:11.576 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 17:40:11.577 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 17:40:11.577 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:11.578 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 17:40:11.581 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 17:40:11.583 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:11.583 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 17:40:11.584 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 17:40:11.584 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:11.584 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 17:40:11.585 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 17:40:11.585 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:11.586 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 17:40:11.586 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 17:40:11.586 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:11.587 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 17:40:11.587 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 17:40:11.587 [Information] ECUCommunicationService: Attempting to reconnect to the Vocom adapter
2025-07-05 17:40:11.589 [Information] DummyVocomService: Reconnecting to Vocom device (dummy)
2025-07-05 17:40:11.591 [Information] DummyVocomService: Disconnecting from Vocom device (dummy)
2025-07-05 17:40:11.692 [Information] ECUCommunicationService: Vocom device disconnected: DUMMY-00000000
2025-07-05 17:40:11.705 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 17:40:11.706 [Error] ECUCommunicationService: ECU communication service is not initialized
2025-07-05 17:40:11.707 [Information] DummyVocomService: Disconnected from Vocom device Dummy Vocom Device (dummy)
2025-07-05 17:40:11.709 [Information] DummyVocomService: Connecting to Vocom device  (dummy)
2025-07-05 17:40:11.709 [Error] DummyVocomService: Device is null
2025-07-05 17:40:11.710 [Error] ECUCommunicationService: Failed to reconnect to the Vocom adapter
2025-07-05 17:40:11.711 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-05 17:40:12.712 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-05 17:40:12.712 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 17:40:12.713 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 17:40:12.713 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 17:40:12.713 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 17:40:12.714 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 17:40:12.715 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 17:40:12.715 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 17:40:12.716 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 17:40:12.716 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 17:40:12.717 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 17:40:12.717 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:12.717 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 17:40:12.718 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 17:40:12.718 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:12.718 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 17:40:12.718 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 17:40:12.719 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:12.719 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 17:40:12.719 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 17:40:12.720 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:12.720 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 17:40:12.720 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 17:40:12.720 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 17:40:12.721 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 17:40:12.721 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 17:40:12.721 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:12.722 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 17:40:12.722 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 17:40:12.722 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:12.723 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 17:40:12.723 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 17:40:12.723 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:12.724 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 17:40:12.724 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 17:40:12.724 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:12.725 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 17:40:12.725 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 17:40:12.725 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 17:40:12.727 [Information] DummyVocomService: Connecting to first available Vocom device (dummy)
2025-07-05 17:40:12.728 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 17:40:12.829 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 17:40:12.830 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 17:40:13.032 [Information] ECUCommunicationService: Vocom device connected: DUMMY-00000000
2025-07-05 17:40:13.033 [Information] ECUCommunicationService: Vocom device connected: DUMMY-00000000
2025-07-05 17:40:13.033 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 17:40:13.033 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 17:40:13.034 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 17:40:13.048 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 17:40:13.049 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 17:40:13.052 [Information] BackupService: Initializing backup service
2025-07-05 17:40:13.053 [Information] BackupService: Backup service initialized successfully
2025-07-05 17:40:13.053 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 17:40:13.053 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 17:40:13.056 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 17:40:13.097 [Information] BackupService: Compressing backup data
2025-07-05 17:40:13.115 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-05 17:40:13.116 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 17:40:13.117 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 17:40:13.117 [Information] BackupService: Compressing backup data
2025-07-05 17:40:13.119 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-05 17:40:13.119 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 17:40:13.120 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 17:40:13.120 [Information] BackupService: Compressing backup data
2025-07-05 17:40:13.121 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (451 bytes)
2025-07-05 17:40:13.121 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 17:40:13.122 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 17:40:13.122 [Information] BackupService: Compressing backup data
2025-07-05 17:40:13.128 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-07-05 17:40:13.128 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 17:40:13.128 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 17:40:13.129 [Information] BackupService: Compressing backup data
2025-07-05 17:40:13.130 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-05 17:40:13.130 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 17:40:13.131 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 17:40:13.131 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 17:40:13.131 [Information] BackupService: Compressing backup data
2025-07-05 17:40:13.132 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-05 17:40:13.133 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 17:40:13.133 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 17:40:13.135 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 17:40:13.145 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 17:40:13.156 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 17:40:13.242 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 17:40:13.243 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 17:40:13.244 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 17:40:13.245 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 17:40:13.245 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 17:40:13.247 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 17:40:13.247 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 17:40:13.254 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 17:40:13.255 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 17:40:13.271 [Information] LicensingService: Initializing licensing service
2025-07-05 17:40:13.327 [Information] LicensingService: License information loaded successfully
2025-07-05 17:40:13.330 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 17:40:13.331 [Information] App: Licensing service initialized successfully
2025-07-05 17:40:13.331 [Information] App: License status: Trial
2025-07-05 17:40:13.331 [Information] App: Trial period: 30 days remaining
2025-07-05 17:40:13.332 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 17:40:13.432 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 17:40:13.433 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 17:40:13.483 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 17:40:13.483 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 17:40:13.483 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 17:40:13.484 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 17:40:13.484 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 17:40:13.486 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 17:40:13.486 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 17:40:13.488 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 17:40:13.488 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 17:40:13.489 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 17:40:13.500 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 17:40:13.502 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 17:40:13.502 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 17:40:13.503 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 17:40:13.503 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 17:40:13.503 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 17:40:13.504 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 17:40:13.504 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 17:40:13.504 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 17:40:13.506 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 17:40:13.507 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 17:40:13.507 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 17:40:13.507 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 17:40:13.508 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 17:40:13.508 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 17:40:13.508 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 17:40:13.508 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 17:40:13.515 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 17:40:13.521 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 17:40:13.522 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 17:40:13.537 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 17:40:13.538 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 17:40:13.545 [Information] CANRegisterAccess: Read value 0x8C from register 0x0141 (simulated)
2025-07-05 17:40:13.553 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 17:40:13.559 [Information] CANRegisterAccess: Read value 0x6E from register 0x0141 (simulated)
2025-07-05 17:40:13.565 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 17:40:13.571 [Information] CANRegisterAccess: Read value 0xED from register 0x0141 (simulated)
2025-07-05 17:40:13.571 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 17:40:13.572 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 17:40:13.573 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 17:40:13.579 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 17:40:13.579 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 17:40:13.586 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 17:40:13.586 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 17:40:13.586 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 17:40:13.593 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 17:40:13.593 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 17:40:13.593 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 17:40:13.600 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 17:40:13.600 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 17:40:13.607 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 17:40:13.607 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 17:40:13.614 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 17:40:13.614 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 17:40:13.620 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 17:40:13.620 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 17:40:13.627 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 17:40:13.627 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 17:40:13.634 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 17:40:13.634 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 17:40:13.641 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 17:40:13.641 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 17:40:13.648 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 17:40:13.648 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 17:40:13.654 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 17:40:13.655 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 17:40:13.660 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 17:40:13.661 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 17:40:13.668 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 17:40:13.668 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 17:40:13.674 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 17:40:13.675 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 17:40:13.681 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 17:40:13.682 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 17:40:13.688 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 17:40:13.689 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 17:40:13.695 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 17:40:13.696 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 17:40:13.702 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 17:40:13.703 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 17:40:13.708 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 17:40:13.709 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 17:40:13.709 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 17:40:13.715 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 17:40:13.716 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 17:40:13.716 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 17:40:13.717 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 17:40:13.722 [Information] CANRegisterAccess: Read value 0x4E from register 0x0141 (simulated)
2025-07-05 17:40:13.723 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 17:40:13.723 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 17:40:13.724 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 17:40:13.724 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 17:40:13.730 [Information] CANRegisterAccess: Read value 0xE7 from register 0x0140 (simulated)
2025-07-05 17:40:13.736 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 17:40:13.742 [Information] CANRegisterAccess: Read value 0x27 from register 0x0140 (simulated)
2025-07-05 17:40:13.747 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 17:40:13.753 [Information] CANRegisterAccess: Read value 0x60 from register 0x0140 (simulated)
2025-07-05 17:40:13.759 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 17:40:13.765 [Information] CANRegisterAccess: Read value 0x7F from register 0x0140 (simulated)
2025-07-05 17:40:13.766 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 17:40:13.766 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 17:40:13.767 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 17:40:13.767 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 17:40:13.778 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 17:40:13.779 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 17:40:13.780 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 17:40:13.783 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 17:40:13.934 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 17:40:13.936 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 17:40:13.937 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 17:40:13.937 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 17:40:13.938 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 17:40:13.948 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 17:40:13.949 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 17:40:13.950 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 17:40:13.960 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 17:40:13.971 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 17:40:13.982 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 17:40:13.993 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 17:40:14.004 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 17:40:14.005 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 17:40:14.006 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 17:40:14.016 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 17:40:14.017 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 17:40:14.018 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 17:40:14.028 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 17:40:14.039 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 17:40:14.050 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 17:40:14.061 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 17:40:14.072 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 17:40:14.083 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 17:40:14.084 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 17:40:14.084 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 17:40:14.095 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 17:40:14.097 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 17:40:14.097 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 17:40:14.097 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 17:40:14.098 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 17:40:14.098 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 17:40:14.098 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 17:40:14.098 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 17:40:14.099 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 17:40:14.099 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 17:40:14.099 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 17:40:14.099 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 17:40:14.100 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 17:40:14.100 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 17:40:14.100 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 17:40:14.101 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 17:40:14.101 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 17:40:14.201 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 17:40:14.201 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 17:40:14.202 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 17:40:14.202 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:14.202 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 17:40:14.203 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 17:40:14.203 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:14.203 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 17:40:14.204 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 17:40:14.204 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:14.204 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 17:40:14.204 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 17:40:14.205 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 17:40:14.205 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 17:40:14.205 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 17:40:14.212 [Information] BackupService: Initializing backup service
2025-07-05 17:40:14.212 [Information] BackupService: Backup service initialized successfully
2025-07-05 17:40:14.263 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 17:40:14.263 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 17:40:14.265 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 17:40:14.265 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 17:40:14.316 [Information] BackupService: Getting predefined backup categories
2025-07-05 17:40:14.323 [Information] MainViewModel: Services initialized successfully
2025-07-05 17:40:26.740 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 17:40:26.741 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 17:40:26.833 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 17:40:26.837 [Information] MainViewModel: Found 1 Vocom device(s)
